# Selectpage 数据源配置修复

## 问题描述

在设备编辑页面中，关联管理员ID和关联商户ID的下拉选择框选不到人，无法正常选择用户。

## 问题原因

Selectpage 组件的 `data-source` 配置错误，指向了不存在的控制器路径：

**错误配置：**
- `data-source="g/admin/index"` - 不存在的路径
- `data-source="g/merchant/index"` - 不存在的路径
- `data-source="g/operator/index"` - 不存在的路径

**正确配置：**
- `data-source="auth/admin/index"` - 管理员控制器路径

## 修复内容

### 1. 设备编辑页面 (device/edit.html)

**修复前：**
```html
<input id="c-g_admin_id" data-source="g/admin/index" class="form-control selectpage" ...>
<input id="c-g_merchant_id" data-source="g/merchant/index" class="form-control selectpage" ...>
```

**修复后：**
```html
<input id="c-g_admin_id" data-source="auth/admin/index" class="form-control selectpage"
       data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"6"}' ...>
<input id="c-g_merchant_id" data-source="auth/admin/index" class="form-control selectpage"
       data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"7"}' ...>
```

### 2. 设备记录编辑页面 (device_record/edit.html)

**修复前：**
```html
<input id="c-g_admin_id" data-source="g/admin/index" class="form-control selectpage" ...>
<input id="c-g_merchant_id" data-source="g/merchant/index" class="form-control selectpage" ...>
<input id="c-g_operator_id" data-source="g/operator/index" class="form-control selectpage" ...>
```

**修复后：**
```html
<input id="c-g_admin_id" data-source="auth/admin/index" class="form-control selectpage"
       data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"6"}' ...>
<input id="c-g_merchant_id" data-source="auth/admin/index" class="form-control selectpage"
       data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"7"}' ...>
<input id="c-g_operator_id" data-source="auth/admin/index" class="form-control selectpage"
       data-primary-key="id" data-field="nickname" ...>
```

## 配置说明

### Selectpage 参数解释

1. **data-source**: 数据源控制器路径
   - `auth/admin/index` - 管理员列表接口

2. **data-primary-key**: 主键字段
   - `id` - 用户ID字段

3. **data-field**: 显示字段
   - `nickname` - 显示用户昵称

4. **data-params**: 额外参数
   - `{"custom[group_id]":"6"}` - 筛选 group_id=6 的打码管理员
   - `{"custom[group_id]":"7"}` - 筛选 group_id=7 的商家

### 用户组说明

- **group_id=6**: 打码管理员
- **group_id=7**: 商家用户
- **无限制**: 操作员用户（所有用户）

## 修复效果

修复后的效果：

1. **关联管理员ID**: 可以选择 group_id=6 的打码管理员用户
2. **关联商户ID**: 可以选择 group_id=7 的商家用户
3. **操作用户ID**: 可以选择所有用户

## 测试验证

### 测试步骤

1. **设备编辑测试**
   - 进入设备管理页面
   - 点击编辑某个设备
   - 点击"关联管理员ID"下拉框
   - 应该能看到管理员用户列表
   - 点击"关联商户ID"下拉框
   - 应该能看到商户用户列表

2. **设备记录编辑测试**
   - 进入打码记录页面
   - 点击编辑某个记录
   - 测试三个用户选择框是否正常工作

### 预期结果

- ✅ 下拉框能正常加载用户列表
- ✅ 可以正常搜索和选择用户
- ✅ 选择后能正确保存用户ID
- ✅ 根据用户组正确过滤用户

## 相关文件

- `application/admin/view/device/device/edit.html` - 设备编辑页面
- `application/admin/view/device/device_record/edit.html` - 设备记录编辑页面
- `application/admin/controller/auth/Admin.php` - 管理员控制器（数据源）

## 注意事项

1. **数据源路径**: 确保 `data-source` 指向正确的控制器路径
2. **权限控制**: 数据源控制器需要有相应的权限控制
3. **参数格式**: `data-params` 必须是有效的JSON格式
4. **字段映射**: 确保 `data-primary-key` 和 `data-field` 对应数据库字段
