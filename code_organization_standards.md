# 代码组织规范

## 问题说明

之前将 JavaScript 代码写在 HTML 模板文件 `add.html` 中是不规范的做法，存在以下问题：

1. **违反分离原则**：HTML、CSS、JavaScript 应该分离
2. **代码冗余**：相同逻辑可能在多个模板中重复
3. **维护困难**：JavaScript 代码分散在各个模板中难以维护
4. **缓存问题**：模板文件的 JavaScript 不能有效缓存
5. **调试困难**：浏览器开发工具中难以定位和调试

## 正确的代码组织

### 1. 文件结构

```
public/assets/js/backend/device/
├── device_record.js          # 设备记录相关的所有 JavaScript 逻辑
├── device.js                 # 设备管理相关的 JavaScript 逻辑
└── ...

application/admin/view/device/device_record/
├── add.html                  # 只包含 HTML 结构，不包含 JavaScript
├── edit.html                 # 只包含 HTML 结构，不包含 JavaScript
└── ...
```

### 2. JavaScript 代码组织

#### 2.1 模块化结构
```javascript
define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'layer'], function ($, undefined, Backend, Table, Form, Layer) {
    
    // 工具函数
    function setSelectpageValue(selector, value) { ... }
    
    // 全局函数
    window.fillQrCodeData = function(qrData) { ... };
    
    // 内部函数
    function processUrlQrData() { ... }
    
    // 控制器对象
    var Controller = {
        index: function () { ... },
        add: function () { 
            Controller.api.bindevent();
            Controller.api.initQrScanner();
            processUrlQrData();  // 调用内部函数
        },
        edit: function () { ... },
        api: {
            bindevent: function () { ... },
            initQrScanner: function () { ... }
        }
    };
    
    return Controller;
});
```

#### 2.2 函数职责分离

**全局函数（window.*）：**
- 需要在其他模块中调用的函数
- 例如：`window.fillQrCodeData`

**内部函数：**
- 只在当前模块内使用的函数
- 例如：`processUrlQrData`

**控制器方法：**
- 页面初始化逻辑
- 事件绑定
- API 调用

### 3. HTML 模板规范

#### 3.1 只包含结构
```html
<!-- add.html -->
<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">设备序列号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_serial_number" class="form-control" name="row[g_serial_number]" type="text" value="{$params.g_serial_number}">
        </div>
    </div>
    <!-- 其他表单字段 -->
</form>

<!-- 不要在这里写 JavaScript 代码 -->
```

#### 3.2 避免内联 JavaScript
```html
<!-- 错误做法 -->
<script>
$(document).ready(function() {
    // JavaScript 代码
});
</script>

<!-- 正确做法 -->
<!-- 所有 JavaScript 逻辑都在 device_record.js 中 -->
```

## 修复后的实现

### 1. device_record.js 中的完整逻辑

```javascript
// URL参数处理函数
function processUrlQrData() {
    var urlParams = new URLSearchParams(window.location.search);
    var qrDataParam = urlParams.get('qrData');
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1' && qrDataParam) {
        try {
            var qrData = JSON.parse(decodeURIComponent(qrDataParam));
            console.log('从URL解析的二维码数据：', qrData);
            
            setTimeout(function() {
                if (typeof window.fillQrCodeData === 'function') {
                    window.fillQrCodeData(qrData);
                }
            }, 500);
            
        } catch (e) {
            console.error('解析URL中的二维码数据失败：', e);
            Toastr.error('二维码数据格式错误');
        }
    }
}

// 在页面初始化时调用
add: function () {
    Controller.api.bindevent();
    Controller.api.initQrScanner();
    processUrlQrData();  // 处理URL参数
}
```

### 2. add.html 中的简化内容

```html
<!-- 只保留一行注释说明 -->
<!-- URL参数处理已移动到 device_record.js 中 -->
```

## 优势

### 1. 代码集中管理
- 所有设备记录相关的 JavaScript 代码都在一个文件中
- 便于查找、修改和维护

### 2. 复用性强
- 函数可以在不同页面间复用
- 避免代码重复

### 3. 调试友好
- 浏览器开发工具中可以直接定位到 JS 文件
- 支持断点调试和性能分析

### 4. 缓存优化
- JavaScript 文件可以被浏览器有效缓存
- 提高页面加载性能

### 5. 团队协作
- 前端开发者专注于 JavaScript 文件
- 后端开发者专注于模板结构
- 职责分离明确

## 最佳实践

### 1. 命名规范
- 文件名：小写字母 + 下划线
- 函数名：驼峰命名法
- 全局函数：明确的命名空间

### 2. 注释规范
```javascript
/**
 * 处理二维码数据填充
 * @param {Object} qrData 二维码数据对象
 */
window.fillQrCodeData = function(qrData) {
    // 实现逻辑
};
```

### 3. 错误处理
- 统一的错误处理机制
- 友好的用户提示
- 详细的控制台日志

### 4. 性能优化
- 避免全局变量污染
- 合理使用闭包
- 延迟加载非关键功能

现在代码组织更加规范，符合前端开发的最佳实践！
