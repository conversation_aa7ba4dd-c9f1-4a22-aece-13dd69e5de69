# 自动选择后置摄像头实现

## 功能概述

实现扫码器自动选择并启动后置摄像头，提供最佳的扫码体验。

## 实现策略

### 1. 智能摄像头检测

```javascript
// 尝试使用后置摄像头启动
function tryStartWithBackCamera(layerIndex) {
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        navigator.mediaDevices.enumerateDevices().then(function(devices) {
            var videoDevices = devices.filter(device => device.kind === 'videoinput');
            console.log('可用摄像头：', videoDevices);
            
            // 查找后置摄像头
            var backCamera = videoDevices.find(device => 
                device.label.toLowerCase().includes('back') || 
                device.label.toLowerCase().includes('environment') ||
                device.label.toLowerCase().includes('rear') ||
                device.label.toLowerCase().includes('后置') ||
                device.label.toLowerCase().includes('后摄')
            );
            
            if (backCamera) {
                console.log('找到后置摄像头：', backCamera.label, backCamera.deviceId);
                // 使用指定的后置摄像头启动
                startWithSpecificCamera(backCamera.deviceId, layerIndex);
            } else {
                console.log('未找到后置摄像头，使用默认配置');
                // 使用默认配置，优先environment
                startWithDefaultConfig(layerIndex);
            }
        });
    }
}
```

### 2. 后置摄像头识别规则

**英文标识：**
- `back`: 后置
- `environment`: 环境摄像头（通常是后置）
- `rear`: 后方

**中文标识：**
- `后置`: 后置摄像头
- `后摄`: 后摄像头

**设备标签示例：**
- "Back Camera"
- "Camera 0, Facing back"
- "环境摄像头"
- "后置摄像头"

### 3. 指定摄像头启动

```javascript
// 使用指定摄像头启动
function startWithSpecificCamera(cameraId, layerIndex) {
    var config = {
        fps: 10,
        qrbox: 250,
        rememberLastUsedCamera: true,
        showTorchButtonIfSupported: true,
        // 指定使用的摄像头
        cameraIdOrConfig: cameraId
    };

    var html5QrcodeScanner = new Html5QrcodeScanner(
        "qr-reader",
        config,
        /* verbose= */ false
    );
    
    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    
    // 自动启动摄像头
    setTimeout(function() {
        var startButton = document.getElementById('html5-qrcode-button-camera-start');
        if (startButton) {
            startButton.click();
            console.log('自动启动后置摄像头');
        }
    }, 1000);
}
```

### 4. 降级策略

```javascript
// 使用默认配置启动
function startWithDefaultConfig(layerIndex) {
    var config = {
        fps: 10,
        qrbox: 250,
        rememberLastUsedCamera: true,
        showTorchButtonIfSupported: true,
        // 优先使用后置摄像头
        facingMode: "environment"
    };

    var html5QrcodeScanner = new Html5QrcodeScanner(
        "qr-reader",
        config,
        /* verbose= */ false
    );

    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    
    // 自动启动摄像头
    setTimeout(function() {
        var startButton = document.getElementById('html5-qrcode-button-camera-start');
        if (startButton) {
            startButton.click();
            console.log('自动启动默认摄像头（优先后置）');
        }
    }, 1000);
}
```

## 工作流程

### 1. 启动流程
```
点击"通用打码" → 
初始化扫码器 → 
获取摄像头列表 → 
查找后置摄像头 → 
启动指定摄像头 → 
自动点击开始按钮
```

### 2. 降级流程
```
未找到后置摄像头 → 
使用 facingMode: "environment" → 
让浏览器自动选择后置摄像头 → 
自动点击开始按钮
```

### 3. 错误处理
```
获取摄像头列表失败 → 
降级到默认配置 → 
使用 facingMode: "environment" → 
自动点击开始按钮
```

## 配置参数说明

### 1. cameraIdOrConfig
- **作用**：指定使用特定的摄像头设备
- **值**：摄像头的 deviceId
- **优先级**：最高，直接使用指定摄像头

### 2. facingMode
- **作用**：指定摄像头朝向
- **值**：`"environment"` (后置) 或 `"user"` (前置)
- **优先级**：中等，浏览器会尝试选择对应朝向的摄像头

### 3. rememberLastUsedCamera
- **作用**：记住用户上次选择的摄像头
- **值**：`true` 或 `false`
- **优先级**：低，只在用户手动选择后生效

## 兼容性处理

### 1. 设备支持检测
```javascript
if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
    // 支持摄像头枚举
} else {
    // 不支持，使用默认配置
}
```

### 2. 权限处理
- 自动请求摄像头权限
- 处理权限被拒绝的情况
- 提供友好的错误提示

### 3. 浏览器兼容
- **Chrome 53+**: 完全支持
- **Firefox 36+**: 完全支持
- **Safari 11+**: 完全支持
- **Edge 12+**: 完全支持

## 调试信息

### 1. 控制台输出
```
可用摄像头： [Array of camera devices]
找到后置摄像头： Back Camera [device-id]
自动启动后置摄像头
```

### 2. 摄像头信息
```javascript
// 查看摄像头详情
navigator.mediaDevices.enumerateDevices().then(devices => {
    devices.filter(d => d.kind === 'videoinput').forEach(camera => {
        console.log('摄像头：', camera.label, camera.deviceId);
    });
});
```

## 用户体验

### 1. 自动化程度
- **完全自动**：无需用户任何操作
- **智能选择**：自动选择最适合的后置摄像头
- **即开即用**：打开扫码窗口立即开始扫码

### 2. 扫码效果
- **后置摄像头**：更好的对焦能力
- **更高分辨率**：通常后置摄像头分辨率更高
- **更稳定**：后置摄像头防抖效果更好

### 3. 降级体验
- **智能降级**：找不到后置摄像头时自动使用默认配置
- **用户选择**：用户仍可手动切换摄像头
- **记忆功能**：记住用户的选择偏好

## 测试验证

### 1. 功能测试
1. 在有多个摄像头的设备上测试
2. 验证是否自动选择后置摄像头
3. 检查扫码效果和稳定性

### 2. 兼容性测试
1. 不同浏览器测试
2. 不同设备类型测试
3. 权限拒绝情况测试

### 3. 降级测试
1. 只有前置摄像头的设备
2. 摄像头权限被拒绝
3. 浏览器不支持摄像头枚举

现在扫码器会智能地自动选择并启动后置摄像头！
