# 统一的 fillQrCodeData 函数

## 功能概述

将所有二维码数据处理逻辑统一到 `window.fillQrCodeData` 函数中，包括：
- 基本字段填充
- enable 参数的复杂处理逻辑
- 设备验证状态设置

## 统一函数实现

```javascript
// 统一的二维码数据填充函数
window.fillQrCodeData = function(qrData) {
    console.log('填充二维码数据：', qrData);
    
    // 直接设置关键字段
    if (qrData.playin) {
        $('#c-g_total_plays').val(qrData.playin);
        console.log('设置总玩次数：', qrData.playin);
    }
    if (qrData.playout) {
        $('#c-g_total_wins').val(qrData.playout);
        console.log('设置总赢次数：', qrData.playout);
    }
    if (qrData.chk) {
        $('#c-g_chk').val(qrData.chk);
        console.log('设置校验码：', qrData.chk);
    }
    if (qrData.In) {
        $('#c-g_in').val(qrData.In);
        console.log('设置In：', qrData.In);
    }
    if (qrData.Out) {
        $('#c-g_out').val(qrData.Out);
        console.log('设置Out：', qrData.Out);
    }
    
    // 处理enable参数
    if (qrData.enable) {
        console.log('处理enable参数：', qrData.enable);
        
        // 先清空选择
        $('#c-g_enable option[value=""]').prop('selected', true);

        if (qrData.enable && qrData.enable.includes('t')) {
            $('#c-g_enable option[value="t"]').prop('selected', true);
        }
        if(qrData.enable && qrData.enable.includes('c')){
            $('#c-g_enable option[value="c"]').prop('selected', true);
        }
        if(qrData.enable && qrData.enable.includes('ct')){
            $('#c-g_enable option[value="ct"]').prop('selected', true);
        }
        $("#c-g_enable").trigger('change');
        
        if(qrData.enable && qrData.enable.includes('r')){
            $('#c-g_cannon_value').prop('checked', true);
        }
        if(qrData.enable && qrData.enable.includes('m')){
            $('#c-g_fish_odds').prop('checked', true);
        }
    }
    
    // 设置设备验证状态
    window.deviceValidated = true;
    if ($('#device-status').length > 0) {
        $('#device-status').html('<span class="text-success">✓ 设备验证成功（来自二维码）</span>');
    }
    
    Toastr.success('二维码数据已填充');
};
```

## 调用场景

### 1. URL参数处理
```javascript
// URL参数处理函数 - 简化版本
function processUrlQrData() {
    var urlParams = new URLSearchParams(window.location.search);
    var qrDataParam = urlParams.get('qrData');
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1' && qrDataParam) {
        try {
            var qrData = JSON.parse(decodeURIComponent(qrDataParam));
            console.log('从URL解析的二维码数据：', qrData);
            
            setTimeout(function() {
                // 调用统一的填充函数
                window.fillQrCodeData(qrData);
            }, 500);
            
        } catch (e) {
            console.error('解析URL中的二维码数据失败：', e);
            Toastr.error('二维码数据格式错误');
        }
    }
}
```

### 2. 扫码成功处理
```javascript
function onScanSuccess(decodedText, decodedResult) {
    try {
        var qrData = JSON.parse(decodedText);
        
        // 停止扫码
        html5QrcodeScanner.clear();
        $("#scanModal").modal('hide');

        // 调用统一的填充函数
        window.fillQrCodeData(qrData);
        
    } catch (e) {
        console.error('扫码数据解析失败：', e);
        Toastr.error('扫码数据格式错误');
    }
}
```

## 处理的字段

### 1. 基本字段
| 二维码字段 | 表单字段 | 说明 |
|-----------|---------|------|
| playin | g_total_plays | 总玩次数 |
| playout | g_total_wins | 总赢次数 |
| chk | g_chk | 校验码 |
| In | g_in | 输入值 |
| Out | g_out | 输出值 |

### 2. Enable 参数处理

**enable 字段的值可能包含：**
- `t`: 设置 g_enable 为 "t"
- `c`: 设置 g_enable 为 "c"  
- `ct`: 设置 g_enable 为 "ct"
- `r`: 勾选 g_cannon_value 复选框
- `m`: 勾选 g_fish_odds 复选框

**处理逻辑：**
```javascript
// 先清空选择
$('#c-g_enable option[value=""]').prop('selected', true);

// 根据包含的字符设置选项
if (qrData.enable.includes('t')) {
    $('#c-g_enable option[value="t"]').prop('selected', true);
}
if (qrData.enable.includes('c')) {
    $('#c-g_enable option[value="c"]').prop('selected', true);
}
if (qrData.enable.includes('ct')) {
    $('#c-g_enable option[value="ct"]').prop('selected', true);
}

// 触发变化事件
$("#c-g_enable").trigger('change');

// 设置复选框
if (qrData.enable.includes('r')) {
    $('#c-g_cannon_value').prop('checked', true);
}
if (qrData.enable.includes('m')) {
    $('#c-g_fish_odds').prop('checked', true);
}
```

## 优势

### 1. 代码统一
- 所有二维码数据处理逻辑集中在一个函数中
- 避免代码重复和不一致

### 2. 易于维护
- 修改处理逻辑只需要改一个地方
- 新增字段处理很简单

### 3. 调试友好
- 统一的日志输出
- 清晰的处理流程

### 4. 功能完整
- 支持所有类型的字段处理
- 包含复杂的 enable 参数逻辑
- 自动设置设备验证状态

## 测试验证

### 1. 基本字段测试
二维码数据：
```json
{
    "dev": "00900356",
    "playin": "528930",
    "playout": "531090",
    "chk": "12131",
    "In": "0",
    "Out": "0"
}
```

预期结果：
- 总玩次数：528930
- 总赢次数：531090
- 校验码：12131

### 2. Enable 参数测试
二维码数据：
```json
{
    "dev": "00900356",
    "enable": "trm"
}
```

预期结果：
- g_enable 下拉框选择 "t"
- g_cannon_value 复选框勾选
- g_fish_odds 复选框勾选

### 3. 控制台输出
```
填充二维码数据： {dev: "00900356", playin: "528930", playout: "531090", ...}
设置总玩次数： 528930
设置总赢次数： 531090
设置校验码： 12131
处理enable参数： trm
```

## 使用场景

1. **URL访问**：通过包含 qrData 的URL打开页面
2. **扫码操作**：在页面中扫描二维码
3. **数据导入**：从其他系统导入二维码数据
4. **测试调试**：手动调用函数测试数据填充

现在所有二维码数据处理都统一到 `fillQrCodeData` 函数中了！
