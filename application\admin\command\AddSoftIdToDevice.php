<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

class AddSoftIdToDevice extends Command
{
    protected function configure()
    {
        $this->setName('add_soft_id_to_device')
            ->setDescription('Add g_soft_id field to gc_device table');
    }

    protected function execute(Input $input, Output $output)
    {
        $tableName = 'gc_device';

        // 检查字段是否已存在
        $hasField = false;
        $fields = Db::query("SHOW COLUMNS FROM {$tableName}");
        foreach ($fields as $field) {
            if ($field['Field'] == 'g_soft_id') {
                $hasField = true;
                break;
            }
        }

        // 如果字段不存在，则添加
        if (!$hasField) {
            Db::execute("ALTER TABLE {$tableName} ADD COLUMN `g_soft_id` VARCHAR(32) NULL COMMENT '设备唯一ID' AFTER `g_id`");
            $output->writeln("Field g_soft_id added to {$tableName} table");

            // 为现有记录生成唯一ID
            $devices = Db::table($tableName)->select();
            foreach ($devices as $device) {
                if (empty($device['g_soft_id'])) {
                    $softId = $this->generateSoftId();
                    Db::table($tableName)->where('g_id', $device['g_id'])->update(['g_soft_id' => $softId]);
                    $output->writeln("Device ID: {$device['g_id']} updated with soft ID: {$softId}");
                } else {
                    $output->writeln("Device ID: {$device['g_id']} already has soft ID: {$device['g_soft_id']}");
                }
            }
            $output->writeln("Generated soft IDs for existing devices");
        } else {
            $output->writeln("Field g_soft_id already exists in {$tableName} table");

            // 为空的g_soft_id记录生成唯一ID
            $emptyIdDevices = Db::table($tableName)->where('g_soft_id', '')->whereOr('g_soft_id', 'null')->whereOr('g_soft_id', null)->column('g_id');
            if ($emptyIdDevices) {
                $output->writeln("Found " . count($emptyIdDevices) . " devices with empty g_soft_id");
                foreach ($emptyIdDevices as $deviceId) {
                    // 生成唯一ID并确保不重复
                    $softId = $this->generateUniqueSoftId($tableName);
                    Db::table($tableName)->where('g_id', $deviceId)->update(['g_soft_id' => $softId]);
                    $output->writeln("Device ID: {$deviceId} updated with soft ID: {$softId}");
                }
                $output->writeln("Generated soft IDs for devices with empty g_soft_id");
            } else {
                $output->writeln("No devices with empty g_soft_id found");
            }
        }
    }

    /**
     * 生成32位唯一ID
     * @return string
     */
    protected function generateSoftId()
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $softId = '';
        for ($i = 0; $i < 32; $i++) {
            $softId .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $softId;
    }

    /**
     * 生成不重复的唯一ID
     * @param string $tableName 表名
     * @return string
     */
    protected function generateUniqueSoftId($tableName)
    {
        $maxAttempts = 10; // 最大尝试次数
        $attempts = 0;

        do {
            $softId = $this->generateSoftId();
            $exists = Db::table($tableName)->where('g_soft_id', $softId)->find();
            $attempts++;
        } while ($exists && $attempts < $maxAttempts);

        // 如果多次尝试后仍然存在重复，则添加时间戳确保唯一性
        if ($exists) {
            $softId = $this->generateSoftId() . time();
        }

        return $softId;
    }
}
