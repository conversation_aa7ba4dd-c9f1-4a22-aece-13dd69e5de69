# 扫码跳转调试指南

## 问题描述

扫码后跳转到新的打码窗口，但无法正确读取到 `g_id`。

## 调试步骤

### 1. 检查二维码数据格式

确认二维码中包含的数据格式，常见的字段映射：

```javascript
// 二维码数据示例
{
    "device_id": "123",      // 设备ID -> g_device_id
    "dev": "DEV001",         // 设备序列号 -> g_serial_number  
    "rid": "RID001",         // 设备RID -> g_rid
    "game": "GAME001",       // 游戏ID -> g_game_id
    "playin": "100",         // 总玩次数 -> g_total_plays
    "playout": "50",         // 总赢次数 -> g_total_wins
    "chk": "ABC123"          // 校验码 -> g_chk
}
```

### 2. 检查URL参数传递

**扫码成功后的URL应该是：**
```
device/device_record/add?g_device_id=123&g_serial_number=DEV001&g_rid=RID001&g_game_id=GAME001&g_total_plays=100&g_total_wins=50&g_chk=ABC123&from_qr=1
```

**调试方法：**
1. 打开浏览器开发者工具
2. 扫码后查看新窗口的URL
3. 确认参数是否正确传递

### 3. 检查参数解析

在新页面中，检查控制台输出：

```javascript
// 应该看到以下日志
console.log('从URL提取的参数：', qrData);
console.log('处理二维码参数：', qrData);
console.log('填充表单数据：', qrData);
console.log('设置 g_device_id:', qrData.g_device_id);
```

### 4. 检查设备验证

如果有 `g_device_id`，会调用 `getDeviceById()` 函数：

```javascript
// 检查AJAX请求
// URL: device/device_record/validate_device
// 参数: { device_id: deviceId }
```

如果有 `g_serial_number`，会调用 `validateDevice()` 函数：

```javascript
// 检查AJAX请求  
// URL: device/device_record/validate_device
// 参数: { serial_number: serialNumber }
```

### 5. 常见问题排查

#### 问题1：g_device_id 为空
**可能原因：**
- 二维码中没有 `device_id` 字段
- 字段名称不匹配

**解决方法：**
```javascript
// 检查二维码数据中的实际字段名
console.log('原始二维码数据：', qrData);

// 如果字段名不是 device_id，需要修改映射
if (qrData.id !== undefined) urlParams.push('g_device_id=' + encodeURIComponent(qrData.id));
```

#### 问题2：设备验证失败
**可能原因：**
- 设备不存在于数据库中
- 权限不足（group_id=7用户限制）

**解决方法：**
1. 检查数据库中是否存在该设备
2. 确认当前用户是否有权限访问该设备

#### 问题3：表单字段未填充
**可能原因：**
- 字段ID不匹配
- selectpage组件未正确初始化

**解决方法：**
```javascript
// 检查字段是否存在
console.log('g_device_id字段：', $('#c-g_device_id').length);
console.log('当前值：', $('#c-g_device_id').val());
```

## 修改建议

### 1. 增强调试信息

在扫码成功处理中添加更多日志：

```javascript
function onScanSuccess(decodedText, decodedResult) {
    try {
        var qrData = JSON.parse(decodedText);
        console.log('扫码原始数据：', qrData);
        
        // 构建URL参数前检查数据
        console.log('device_id:', qrData.device_id);
        console.log('dev:', qrData.dev);
        
        // ... 其他代码
    } catch (e) {
        console.error('扫码数据解析失败：', e);
        console.error('原始数据：', decodedText);
    }
}
```

### 2. 兼容多种字段名

```javascript
// 支持多种可能的字段名
var deviceId = qrData.device_id || qrData.id || qrData.g_id;
if (deviceId !== undefined) {
    urlParams.push('g_device_id=' + encodeURIComponent(deviceId));
}
```

### 3. 添加错误处理

```javascript
function processQrParams(qrData) {
    console.log('处理二维码参数：', qrData);
    
    if (!qrData.g_device_id && !qrData.g_serial_number) {
        Toastr.error('二维码中缺少设备信息');
        return;
    }
    
    // ... 其他处理逻辑
}
```

## 测试步骤

### 1. 手动测试URL参数

直接在浏览器中访问：
```
/admin/device/device_record/add?g_device_id=123&g_serial_number=DEV001&from_qr=1
```

### 2. 检查控制台输出

扫码后查看浏览器控制台，应该看到：
- 扫码原始数据
- URL参数提取结果
- 设备验证请求和响应
- 表单填充过程

### 3. 检查网络请求

在开发者工具的Network标签中查看：
- `validate_device` 请求是否发送
- 请求参数是否正确
- 响应数据是否包含完整的设备信息

## 预期结果

正确实现后应该看到：

1. **扫码成功**：显示"扫码成功，正在跳转..."
2. **窗口关闭**：原通用打码窗口关闭
3. **新窗口打开**：新的打码窗口打开
4. **参数填充**：所有字段自动填充正确的值
5. **设备验证**：显示"✓ 设备验证成功"
6. **g_device_id设置**：隐藏字段 `g_device_id` 有正确的值

## 常用调试命令

```javascript
// 在浏览器控制台中执行
console.log('g_device_id值：', $('#c-g_device_id').val());
console.log('URL参数：', new URLSearchParams(window.location.search));
console.log('from_qr标识：', new URLSearchParams(window.location.search).get('from_qr'));
```
