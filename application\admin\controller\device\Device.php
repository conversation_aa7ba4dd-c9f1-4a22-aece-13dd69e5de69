<?php

namespace app\admin\controller\device;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 设备管理
 *
 * @icon fa fa-circle-o
 */
class Device extends Backend
{

    /**
     * Device模型对象
     * @var \app\admin\model\device\Device
     */
    protected $model = null;

    /**
     * 是否开启数据限制
     * 对于 group_id=7 的用户，启用基于 g_merchant_id 的数据限制
     */
    protected $dataLimit = 'auth';

    /**
     * 数据限制字段
     * 对于 group_id=7 的用户，使用 g_merchant_id 字段进行限制
     */
    protected $dataLimitField = 'g_merchant_id';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\device\Device;
        $this->searchFields = 'g_id,g_serial_number,g_rid,g_batch_number';
        $this->view->assign("gStatusList", $this->model->getGStatusList());

        // 对于 group_id=7 的用户，启用特殊的数据权限控制
        if ($this->auth->group_id == 7) {
            $this->dataLimit = 'merchant';
            $this->dataLimitField = 'g_merchant_id';
        }
    }



    /**
     * 获取数据限制的管理员ID
     * 对于 group_id=7 的用户，返回当前用户ID用于 g_merchant_id 字段过滤
     * @return mixed
     */
    protected function getDataLimitAdminIds()
    {
        if (!$this->dataLimit) {
            return null;
        }
        if ($this->auth->isSuperAdmin()) {
            return null;
        }

        // 对于 group_id=7 的商户用户，直接限制只能查看自己的数据
        if ($this->auth->group_id == 7) {
            // 返回当前用户ID，用于 g_merchant_id 字段过滤
            return [$this->auth->id];
        }

        // // 其他用户组使用默认逻辑
        // $adminIds = [];
        // if (in_array($this->dataLimit, ['auth', 'personal'])) {
        //     $adminIds = $this->dataLimit == 'auth' ? $this->auth->getChildrenAdminIds(true) : [$this->auth->id];
        // }
        // return $adminIds;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 生成32位唯一ID
     * @return string
     */
    protected function generateSoftId()
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $softId = '';
        for ($i = 0; $i < 16; $i++) {
            $softId .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $softId;
    }

    /**
     * 生成不重复的唯一ID
     * @return string
     */
    protected function generateUniqueSoftId()
    {
        $tableName = 'gc_device';
        $maxAttempts = 10; // 最大尝试次数
        $attempts = 0;

        do {
            $softId = $this->generateSoftId();
            $exists = Db::table($tableName)->where('g_soft_id', $softId)->find();
            $attempts++;
        } while ($exists && $attempts < $maxAttempts);

        // 如果多次尝试后仍然存在重复，则添加时间戳确保唯一性
        if ($exists) {
            $softId = $this->generateSoftId();
        }

        return $softId;
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        // 判断当前用户是否有权限查看g_soft_id字段
        $canViewSoftId = $this->auth->group_id != 7 ;
        if (!$canViewSoftId) {
            // 如果没有权限，则隐藏g_soft_id字段
            $row['g_soft_id'] = '';
        }

        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 添加
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 生成不重复的唯一ID
        $params['g_soft_id'] = $this->generateUniqueSoftId();

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }

            
            $params['g_updatetime'] = time();
            $params['g_createtime'] = time();
            $params['g_enable_type'] = 2;
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }
}