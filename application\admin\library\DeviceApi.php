<?php

namespace app\admin\library;

use think\Log;

/**
 * 设备API通信类
 */
class DeviceApi
{
    /**
     * API服务器地址
     */
    protected $apiUrl = 'http://120.79.11.58:9202/report';
    
    /**
     * 发送打码数据到服务器
     * 
     * @param array $deviceRecord 设备打码记录数据
     * @return array 返回结果，包含code和msg
     */
    public function sendDeviceRecord($deviceRecord)
    {

        // 构建请求数据
        $data = [
            'state' => 'reportex',
            'info' => $this->formatInfo($deviceRecord),
            'enable' => $this->formatEnable($deviceRecord),
            'action' => $this->formatAction($deviceRecord),
            'mode' => intval($deviceRecord['g_mode']),
            'wave' => intval($deviceRecord['g_wave']),
            'rate' => intval($deviceRecord['g_rate']),
            'back' => intval($deviceRecord['g_back']),
            'limit' => intval($deviceRecord['g_limit']) == 0 ? 0 : intval($deviceRecord['g_limit'])/1000,
            'time' => intval($deviceRecord['g_time']) == 0 ? 0 : intval($deviceRecord['g_time'])/1000
        ];
        

        Log::record('设备打码记录数据: ' . json_encode($data), 'info');
        
        // 发送请求
        $result = $this->sendRequest($data);
        
        Log::record('设备打码记录结果: ' . json_encode($result), 'info');
        return $result;
    }
    
    /**
     * 格式化info字段
     * 
     * @param array $deviceRecord 设备打码记录数据
     * @return string 格式化后的info字段
     */
    protected function formatInfo($deviceRecord)
    {
        // 将序列号转换为16进制
        $serialHex = $this->convertToHex($deviceRecord['g_serial_number']);
        
        // 获取识别码
        $g_chk = $deviceRecord['g_chk'];
        
        // 格式：序列号 游戏代号:总玩-总赢-识别码
        return sprintf(
            "%s %s:%s-%s-%s",
            $serialHex,
            $deviceRecord['g_game_id'],
            $deviceRecord['g_total_plays'],
            $deviceRecord['g_total_wins'],
            $g_chk
        );
    }
    
    /**
     * 将序列号转换为16进制
     * 
     * @param string $serialNumber 序列号
     * @return string 16进制表示
     */
    protected function convertToHex($serialNumber)
    {
        // 如果序列号是数字，则转换为16进制
        if (is_numeric($serialNumber)) {
            return  substr("0000".strtoupper(dechex(intval($serialNumber))),-6);
        }
        
        // 如果已经是16进制格式，则直接返回
        return substr("0000".strtoupper($serialNumber),-6);
    }
    
    /**
     * 格式化enable字段
     * 
     * @param array $deviceRecord 设备打码记录数据
     * @return string 格式化后的enable字段
     */
    protected function formatEnable($deviceRecord)
    {
        $enable = '';
        
        // 允许清除总的账目
        if (!empty($deviceRecord['g_fish_odds']) and $deviceRecord['g_fish_odds']==1) {
            $enable .= 'm';
        }

        // 允许清除当期账目
        if (!empty($deviceRecord['g_cannon_value']) and $deviceRecord['g_cannon_value']==1) {
            $enable .= 'r';
        }

        // 对应币数
        if (!empty($deviceRecord['g_enable'])) {
            $enable .= $deviceRecord['g_enable'];
        }
        
        
        
        
        return $enable ? '' . $enable . '' : '';
    }
    
    /**
     * 格式化action字段
     * 
     * @param array $deviceRecord 设备打码记录数据
     * @return string 格式化后的action字段
     */
    protected function formatAction($deviceRecord)
    {
        $action = '';
        
        // 接受修改模式参数
        if (!empty($deviceRecord['g_accept_modify_mode']) and $deviceRecord['g_accept_modify_mode']==1) {
            $action .= 'm';
        }
        
        // 接受调整游戏几率
        if (!empty($deviceRecord['g_accept_adjust_rate']) and $deviceRecord['g_accept_adjust_rate']==1) {
            $action .= 'r';
        }
        
        // 清除当期账目
        if (!empty($deviceRecord['g_clear_current_account']) and $deviceRecord['g_clear_current_account']==1) {
            $action .= 'c';
        }
        
        // 清除总的账目
        if (!empty($deviceRecord['g_clear_total_account']) and $deviceRecord['g_clear_total_account']==1) {
            $action .= 't';
        }
        
        return $action;
    }
    
    /**
     * 发送HTTP请求
     * 
     * @param array $data 请求数据
     * @return array 返回结果
     */
    protected function sendRequest($data)
    {
        // 将数据转换为JSON
        $jsonData = json_encode($data) . "\r\n";
        
        // 初始化CURL
        $ch = curl_init();
        //var_dump($jsonData);
        // 设置CURL选项
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10秒超时
        
        // 执行CURL请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        // 关闭CURL
        curl_close($ch);
        
        // 处理响应
        if ($error) {
            return [
                'code' => -1,
                'msg' => 'CURL Error: ' . $error
            ];
        }
        
        if ($httpCode != 200) {
            return [
                'code' => -2,
                'msg' => 'HTTP Error: ' . $httpCode
            ];
        }
        
        // 解析JSON响应
        $result = json_decode($response, true);
        if (!$result) {
            return [
                'code' => -3,
                'msg' => 'Invalid JSON response: ' . $response
            ];
        }
        // var_dump($result);
        return $result;
    }
}
