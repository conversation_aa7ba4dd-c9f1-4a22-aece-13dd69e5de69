# 通用打码扫码跳转功能

## 功能流程

### 1. 用户操作流程
1. 在设备管理页面点击"通用打码"按钮
2. 弹出扫码窗口
3. 扫描二维码
4. 自动跳转到打码页面并填充数据

### 2. 技术实现流程
1. **点击按钮** → 显示扫码弹窗
2. **扫码成功** → 解析二维码数据
3. **构建URL** → 包含完整的二维码数据
4. **跳转页面** → 打开打码页面
5. **自动填充** → 后端处理设备信息，前端覆盖二维码数据

## 实现细节

### 1. 设备管理页面修改

**文件：** `public/assets/js/backend/device/device.js`

#### 1.1 通用打码按钮事件
```javascript
// 绑定通用打码按钮事件
$(document).on('click', '.btn-coding', function () {
    // 显示扫码弹窗
    Layer.open({
        type: 1,
        title: '扫码打码',
        area: ['400px', '500px'],
        content: '<div id="qr-scanner-modal">' +
                 '<div id="qr-reader" style="width: 100%; height: 300px;"></div>' +
                 '<div style="text-align: center; margin-top: 10px;">' +
                 '<button type="button" class="btn btn-default" onclick="layer.closeAll()">取消</button>' +
                 '</div>' +
                 '</div>',
        success: function(layero, index) {
            initQrScanner(index);
        }
    });
});
```

#### 1.2 扫码器初始化
```javascript
function initQrScanner(layerIndex) {
    // 动态加载 html5-qrcode 库
    if (typeof Html5QrcodeScanner === 'undefined') {
        var script = document.createElement('script');
        script.src = '/assets/libs/html5-qrcode/html5-qrcode.min.js';
        script.onload = function() {
            startScanner(layerIndex);
        };
        document.head.appendChild(script);
    } else {
        startScanner(layerIndex);
    }
}
```

#### 1.3 扫码成功处理
```javascript
function onScanSuccess(decodedText, decodedResult) {
    try {
        // 解析二维码数据
        var qrData = JSON.parse(decodedText);
        
        // 停止扫码
        html5QrcodeScanner.clear();
        
        // 关闭扫码弹窗
        layer.close(layerIndex);
        
        // 根据设备序列号查询设备信息
        if (qrData.dev) {
            // 构建跳转URL
            var qrDataParam = encodeURIComponent(JSON.stringify(qrData));
            var url = 'device/device_record/add?qrData=' + qrDataParam + '&from_qr=1';
            
            // 跳转到打码页面
            Fast.api.open(url, '打码记录', {
                area: ['90%', '90%'],
                callback: function() {
                    table.bootstrapTable('refresh');
                }
            });
            
            Toastr.success('扫码成功，正在跳转...');
        } else {
            Toastr.error('二维码中缺少设备信息');
        }
        
    } catch (e) {
        console.error('解析二维码数据失败：', e);
        Toastr.error('二维码数据格式错误');
    }
}
```

### 2. 二维码数据格式

**示例数据：**
```json
{
    "dev": "00900356",
    "game": "1005",
    "playin": "528930",
    "playout": "531090",
    "chk": 12131,
    "In": "0",
    "Out": "0",
    "enable": "t"
}
```

**字段说明：**
- `dev`: 设备序列号
- `game`: 游戏ID
- `playin`: 总玩次数
- `playout`: 总赢次数
- `chk`: 校验码
- `In`: 输入值
- `Out`: 输出值
- `enable`: 启用参数

### 3. URL构建

**生成的URL格式：**
```
device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%2C%22chk%22%3A12131%2C%22In%22%3A%220%22%2C%22Out%22%3A%220%22%2C%22enable%22%3A%22t%22%7D&from_qr=1
```

**参数说明：**
- `qrData`: URL编码的JSON字符串
- `from_qr=1`: 标识来自二维码扫描

### 4. 打码页面处理

**后端处理（控制器）：**
1. 检测 `qrData` 和 `from_qr=1` 参数
2. 解析二维码数据，提取 `dev` 字段
3. 根据设备序列号查询设备信息
4. 设置所有设备字段为表单默认值
5. 将 `qrData` 传递给前端

**前端处理（JavaScript）：**
1. 检测URL参数中的 `qrData`
2. 调用 `fillQrCodeData()` 函数
3. 用二维码数据覆盖部分字段值

## 数据流转

### 1. 扫码阶段
```
设备管理页面 → 点击"通用打码" → 弹出扫码窗口 → 扫描二维码 → 解析JSON数据
```

### 2. 跳转阶段
```
构建URL参数 → 关闭扫码窗口 → 跳转到打码页面
```

### 3. 填充阶段
```
后端：根据dev查询设备 → 设置默认值
前端：解析qrData → 覆盖部分字段
```

### 4. 最终结果
```
设备信息（来自数据库） + 实时数据（来自二维码） = 完整的打码记录
```

## 与原有功能的区别

### 原有的打码按钮
```javascript
// 表格中每行的打码按钮
{
    name: 'coding',
    text: __('打码'),
    icon: 'fa fa-code',
    classname: 'btn btn-xs btn-info btn-dialog',
    url: 'device/device_record/add?g_device_id={g_id}&g_serial_number={g_serial_number}&g_rid={g_rid}&g_game_id={g_game_id}&g_total_plays={g_total_plays}&g_total_wins={g_total_wins}',
}
```

### 新的通用打码
```javascript
// 工具栏的通用打码按钮
<a href="javascript:;" class="btn btn-info btn-coding" title="通用打码">
    <i class="fa fa-code"></i> 通用打码
</a>
```

**区别：**
1. **原有打码**：针对特定设备，直接传递设备参数
2. **通用打码**：先扫码识别设备，然后传递二维码数据

## 优势

### 1. 灵活性
- 不需要在设备列表中找到特定设备
- 可以扫描任何设备的二维码

### 2. 实时性
- 二维码中包含最新的游戏数据
- 避免数据库中的过期信息

### 3. 便捷性
- 一键扫码即可开始打码
- 减少手动查找和选择的步骤

### 4. 准确性
- 直接从设备获取准确的数据
- 减少人为输入错误

## 测试验证

### 1. 功能测试
1. 在设备管理页面点击"通用打码"
2. 扫描包含设备信息的二维码
3. 验证是否正确跳转到打码页面
4. 检查表单是否正确填充数据

### 2. 数据验证
- 设备序列号：来自二维码
- 游戏ID：来自设备表
- 总玩次数：来自二维码（覆盖设备表）
- 总赢次数：来自二维码（覆盖设备表）
- 其他配置：来自设备表

### 3. 错误处理
- 二维码格式错误：显示"二维码数据格式错误"
- 缺少设备信息：显示"二维码中缺少设备信息"
- 设备不存在：显示"设备不存在，请先添加设备"

现在通用打码功能支持扫码跳转了！
