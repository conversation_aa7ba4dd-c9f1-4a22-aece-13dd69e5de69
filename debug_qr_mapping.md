# 二维码字段映射调试指南

## 问题现象

扫码后，总玩次数和总赢次数字段显示为 0，而不是二维码中的实际值（如 528930 和 531090）。

## 调试步骤

### 1. 检查服务器日志

查看 FastAdmin 的错误日志文件：
```bash
# 通常位置
/www/wwwroot/gamecoder/runtime/log/
# 或者
/www/wwwroot/gamecoder/runtime/log/error_log
```

### 2. 查找调试信息

在日志中查找以下关键信息：

#### 2.1 原始数据
```
原始qrData参数: %7B%22dev%22%3A%2200900356%22...
解码后的qrData: {"dev":"00900356","game":"1005","playin":"528930","playout":"531090"...}
```

#### 2.2 字段映射过程
```
映射字段: playin -> g_total_plays = 528930
映射字段: playout -> g_total_wins = 531090
映射后的params: {"g_total_plays":"528930","g_total_wins":"531090"...}
```

#### 2.3 设备验证后的处理
```
保留二维码的g_total_plays: 528930
保留二维码的g_total_wins: 531090
```

#### 2.4 最终参数
```
最终的params[g_total_plays]: 528930
最终的params[g_total_wins]: 531090
```

### 3. 可能的问题点

#### 3.1 URL参数解析问题
如果看到：
```
原始qrData参数: 
解码后的qrData: null
```
说明 qrData 参数没有正确传递或解析失败。

#### 3.2 字段映射问题
如果看到：
```
直接设置: playin = 528930
直接设置: playout = 531090
```
而不是：
```
映射字段: playin -> g_total_plays = 528930
映射字段: playout -> g_total_wins = 531090
```
说明字段映射没有生效。

#### 3.3 数据覆盖问题
如果看到：
```
使用数据库的g_total_plays: 0
使用数据库的g_total_wins: 0
```
说明二维码数据被数据库数据覆盖了。

#### 3.4 默认参数覆盖问题
如果前面的步骤都正确，但最终参数显示为 0：
```
最终的params[g_total_plays]: 0
最终的params[g_total_wins]: 0
```
说明默认参数覆盖了二维码数据。

## 常见问题和解决方案

### 问题1：qrData 参数为空
**原因：** 前端没有正确构建 qrData 参数
**解决：** 检查 JavaScript 中的 URL 构建逻辑

### 问题2：JSON 解析失败
**原因：** qrData 参数格式错误或编码问题
**解决：** 检查 JSON 编码和 URL 编码

### 问题3：字段映射失效
**原因：** 字段名不匹配或映射表错误
**解决：** 检查二维码中的实际字段名

### 问题4：默认参数覆盖
**原因：** `array_merge($defaultParams, $params)` 中默认参数的优先级问题
**解决：** 调整合并顺序或修改默认参数设置

## 手动测试方法

### 1. 直接访问URL
```
/admin/device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%7D&from_qr=1
```

### 2. 检查页面源码
查看生成的 HTML 中 input 字段的 value 属性：
```html
<input id="c-g_total_plays" value="528930" ...>
<input id="c-g_total_wins" value="531090" ...>
```

### 3. 浏览器控制台测试
```javascript
// 检查字段值
console.log('总玩次数：', $('#c-g_total_plays').val());
console.log('总赢次数：', $('#c-g_total_wins').val());

// 检查URL参数
var urlParams = new URLSearchParams(window.location.search);
console.log('qrData参数：', urlParams.get('qrData'));
console.log('from_qr参数：', urlParams.get('from_qr'));
```

## 修复建议

### 如果是默认参数覆盖问题
修改默认参数的设置方式：
```php
// 只为未设置的字段设置默认值
foreach ($defaultParams as $key => $defaultValue) {
    if (!isset($params[$key])) {
        $params[$key] = $defaultValue;
    }
}
```

### 如果是字段映射问题
检查二维码中的实际字段名：
```php
// 添加调试信息查看二维码原始数据
error_log('二维码原始字段：' . print_r(array_keys($qrData), true));
```

### 如果是数据类型问题
确保数据类型正确：
```php
// 确保数字字段为整数类型
if (isset($params['g_total_plays'])) {
    $params['g_total_plays'] = (int)$params['g_total_plays'];
}
if (isset($params['g_total_wins'])) {
    $params['g_total_wins'] = (int)$params['g_total_wins'];
}
```

## 下一步行动

1. **查看日志**：首先查看服务器错误日志中的调试信息
2. **确定问题点**：根据日志信息确定问题发生在哪个步骤
3. **针对性修复**：根据具体问题点进行修复
4. **验证修复**：重新测试确认问题解决

请先查看日志信息，然后告诉我具体看到了什么调试输出，这样我可以针对性地解决问题。
