# 二维码数据URL参数传递

## 新的实现方式

将完整的二维码数据作为一个URL参数传递，而不是分散成多个参数。

## URL格式对比

### 旧格式（分散参数）
```
device/device_record/add?dev=00900356&game=1005&playin=528930&playout=531090&chk=12131&In=0&Out=0&enable=&from_qr=1&dialog=1
```

### 新格式（统一参数）
```
device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%2C%22chk%22%3A%2212131%22%2C%22In%22%3A%220%22%2C%22Out%22%3A%220%22%2C%22enable%22%3A%22%22%7D&from_qr=1
```

**解码后的qrData参数：**
```json
{
    "dev": "00900356",
    "game": "1005", 
    "playin": "528930",
    "playout": "531090",
    "chk": "12131",
    "In": "0",
    "Out": "0",
    "enable": ""
}
```

## 实现细节

### 1. 扫码成功处理

```javascript
// 将完整的二维码数据作为一个参数传递
var qrDataParam = encodeURIComponent(JSON.stringify(qrData));
var url = 'device/device_record/add?qrData=' + qrDataParam + '&from_qr=1';

parent.Fast.api.open(url, '打码记录', {
    area: ['90%', '90%'],
    callback: function() {
        if (parent.Table && parent.Table.api && parent.Table.api.table) {
            parent.Table.api.table.bootstrapTable('refresh');
        }
    }
});
```

### 2. URL参数解析

```javascript
$(document).ready(function() {
    var urlParams = new URLSearchParams(window.location.search);
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1') {
        var qrDataParam = urlParams.get('qrData');
        var qrData = {};
        
        if (qrDataParam) {
            try {
                qrData = JSON.parse(decodeURIComponent(qrDataParam));
                console.log('从URL提取的二维码数据：', qrData);
                
                // 调用处理函数
                setTimeout(function() {
                    window.processQrCodeData(qrData, true);
                }, 500);
            } catch (e) {
                console.error('解析二维码数据失败：', e);
                Toastr.error('二维码数据格式错误');
            }
        }
    }
});
```

## 字段映射扩展

### 新增字段映射

```javascript
var fieldMapping = {
    'device_id': 'g_device_id',
    'g_id': 'g_device_id',
    'dev': 'g_serial_number',
    'rid': 'g_rid', 
    'game': 'g_game_id',
    'playin': 'g_total_plays',
    'playout': 'g_total_wins',
    'chk': 'g_chk',
    'In': 'g_in',        // 新增
    'Out': 'g_out'       // 新增
};
```

### Enable参数特殊处理

```javascript
// 处理enable参数（特殊逻辑）
if (qrData.enable !== undefined) {
    console.log('处理enable参数：', qrData.enable);
    
    if (qrData.enable) {
        // 如果enable是JSON字符串，尝试解析
        if (typeof enableData === 'string' && enableData.startsWith('{')) {
            try {
                enableData = JSON.parse(enableData);
                for (var enableKey in enableData) {
                    var enableFieldId = '#c-g_' + enableKey;
                    if ($(enableFieldId).length > 0) {
                        if ($(enableFieldId).is(':checkbox')) {
                            $(enableFieldId).prop('checked', enableData[enableKey] == 1);
                        } else {
                            $(enableFieldId).val(enableData[enableKey]);
                        }
                    }
                }
            } catch (e) {
                console.error('解析enable参数失败：', e);
            }
        } else {
            // 如果enable是简单值，设置到g_enable字段
            $('#c-g_enable').val(enableData);
        }
    }
}
```

## 优势

### 1. 参数完整性
- 保留二维码中的所有原始数据
- 不会因为URL长度限制而丢失参数
- 支持复杂的嵌套数据结构

### 2. 扩展性
- 新增字段无需修改URL构建逻辑
- 支持任意数量的参数
- 便于版本兼容

### 3. 可读性
- URL更简洁
- 调试时容易识别
- 日志记录更清晰

### 4. 安全性
- 统一的编码/解码处理
- 减少参数注入风险
- 便于数据验证

## 测试示例

### 二维码数据示例

```json
{
    "dev": "00900356",
    "game": "1005",
    "playin": "528930", 
    "playout": "531090",
    "chk": "12131",
    "In": "0",
    "Out": "0",
    "enable": "",
    "rid": "RID001",
    "custom_field": "custom_value"
}
```

### 生成的URL

```
device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%2C%22chk%22%3A%2212131%22%2C%22In%22%3A%220%22%2C%22Out%22%3A%220%22%2C%22enable%22%3A%22%22%2C%22rid%22%3A%22RID001%22%2C%22custom_field%22%3A%22custom_value%22%7D&from_qr=1
```

### 解析结果

页面加载时会自动：
1. 提取qrData参数
2. JSON解码获取完整数据
3. 根据g_serial_number获取设备信息
4. 补充g_id到数据中
5. 填充所有表单字段

## 调试方法

### 1. 检查URL参数
```javascript
console.log('qrData参数：', new URLSearchParams(window.location.search).get('qrData'));
```

### 2. 检查解析结果
```javascript
var qrDataParam = new URLSearchParams(window.location.search).get('qrData');
if (qrDataParam) {
    var qrData = JSON.parse(decodeURIComponent(qrDataParam));
    console.log('解析后的数据：', qrData);
}
```

### 3. 验证字段填充
```javascript
console.log('g_device_id：', $('#c-g_device_id').val());
console.log('g_serial_number：', $('#c-g_serial_number').val());
console.log('g_game_id：', $('#c-g_game_id').val());
```

## 注意事项

1. **URL长度限制**：虽然统一了参数，但仍需注意浏览器URL长度限制
2. **编码问题**：确保正确的JSON编码和URL编码
3. **特殊字符**：处理二维码中可能包含的特殊字符
4. **向后兼容**：保持对旧格式的兼容性（如果需要）

## 错误处理

1. **JSON解析失败**：显示"二维码数据格式错误"
2. **参数缺失**：显示"未找到qrData参数"
3. **设备验证失败**：显示具体的错误信息
4. **字段映射失败**：记录日志但不中断流程
