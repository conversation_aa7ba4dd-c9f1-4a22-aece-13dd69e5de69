<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_code" data-rule="required" class="form-control" name="row[game_code]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_name" data-rule="required" class="form-control" name="row[game_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-game_type" data-rule="required" class="form-control selectpicker" name="row[game_type]">
                {foreach name="gameTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>

    <!-- 算法模式控制参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mode_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[mode_config][0][key]" value="0"></td>
                                                <td><input type="text" class="form-control mode-item" data-index="0"  name="row[mode_config][0][value]" value="模式0"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[mode_config][1][key]" value="1"></td>
                                                <td><input type="text" class="form-control mode-item" data-index="1" name="row[mode_config][1][value]" value="模式1"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[mode_config][2][key]" value="2"></td>
                                                <td><input type="text" class="form-control mode-item" data-index="2" name="row[mode_config][2][value]" value="模式2"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[mode_config][3][key]" value="3"></td>
                                                <td><input type="text" class="form-control mode-item" data-index="3" name="row[mode_config][3][value]" value="模式3"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 算法波动参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wave_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[wave_config][0][key]" value="0"></td>
                                                <td><input type="text" class="form-control wave-item" data-index="0" name="row[wave_config][0][value]" value="波动小"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[wave_config][1][key]" value="1"></td>
                                                <td><input type="text" class="form-control wave-item" data-index="1" name="row[wave_config][1][value]" value="波动中小"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[wave_config][2][key]" value="2"></td>
                                                <td><input type="text" class="form-control wave-item" data-index="2" name="row[wave_config][2][value]" value="波动中大"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[wave_config][3][key]" value="3"></td>
                                                <td><input type="text" class="form-control wave-item" data-index="3" name="row[wave_config][3][value]" value="波动大"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 游戏几率配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rate_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][0][key]" value="0"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="0" name="row[rate_config][0][value]" value="容易"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][1][key]" value="1"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="1" name="row[rate_config][1][value]" value="较容易"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][2][key]" value="2"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="2" name="row[rate_config][2][value]" value="中等偏容易"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][3][key]" value="3"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="3" name="row[rate_config][3][value]" value="中等"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][4][key]" value="4"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="4" name="row[rate_config][4][value]" value="中等偏困难"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][5][key]" value="5"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="5" name="row[rate_config][5][value]" value="较困难"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][6][key]" value="6"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="6" name="row[rate_config][6][value]" value="困难"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][7][key]" value="7"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="7" name="row[rate_config][7][value]" value="极难"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 备用参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Back_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][0][key]" value="0"></td>
                            <td><input type="text" class="form-control back-item" data-index="0" name="row[back_config][0][value]" value="默认值"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][1][key]" value="1"></td>
                            <td><input type="text" class="form-control back-item" data-index="1" name="row[back_config][1][value]" value="备用1"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][2][key]" value="2"></td>
                            <td><input type="text" class="form-control back-item" data-index="2" name="row[back_config][2][value]" value="备用2"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][3][key]" value="3"></td>
                            <td><input type="text" class="form-control back-item" data-index="3" name="row[back_config][3][value]" value="备用3"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][4][key]" value="4"></td>
                            <td><input type="text" class="form-control back-item" data-index="4" name="row[back_config][4][value]" value="备用4"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][5][key]" value="5"></td>
                            <td><input type="text" class="form-control back-item" data-index="5" name="row[back_config][5][value]" value="备用5"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][6][key]" value="6"></td>
                            <td><input type="text" class="form-control back-item" data-index="6" name="row[back_config][6][value]" value="备用6"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][7][key]" value="7"></td>
                            <td><input type="text" class="form-control back-item" data-index="7" name="row[back_config][7][value]" value="备用7"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select class="form-control" name="row[enable_type]">
                <option value="1">{:__('Used_for_parameter_enable')}</option>
                <option value="2">{:__('Used_for_setting_coin_score')}</option>
            </select>
        </div>
    </div>

    <!-- 启用参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="text" class="form-control" name="row[enable_config][''][key]" value=""></td>
                            <td><input type="text" class="form-control enable-item" data-index="0" name="row[enable_config][''][value]" value="1币10分"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[enable_config]['t'][key]" value="t"></td>
                            <td><input type="text" class="form-control enable-item" data-index="1" name="row[enable_config]['t'][value]" value="1币100分"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[enable_config]['c'][key]" value="c"></td>
                            <td><input type="text" class="form-control enable-item" data-index="2" name="row[enable_config]['c'][value]" value="1币500分"></td>
                        </tr>
                        <tr>
                            <td><input type="text" class="form-control" name="row[enable_config]['ct'][key]" value="ct"></td>
                            <td><input type="text" class="form-control enable-item" data-index="3" name="row[enable_config]['ct'][value]" value="1币1000分"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 爆机数值配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Limit_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="row[limit_config][range]" value="-32876000-328750000">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="row[limit_config][description]" value="爆机数值，0为不控。">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 游戏局数控制配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Time_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="row[time_config][range]" value="0-32875000">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="row[time_config][description]" value="游戏局数控制，0为不控。">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
