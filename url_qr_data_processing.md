# URL二维码数据处理

## 功能说明

当通过包含 `qrData` 参数的URL直接打开添加页面时，自动解析并填充二维码数据。

## URL格式

```
https://testdevice.91jdcd.com/FQgsXeSAhO.php/device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%2C%22chk%22%3A12131%2C%22In%22%3A%220%22%2C%22Out%22%3A%220%22%2C%22enable%22%3A%22t%22%7D&from_qr=1&dialog=1
```

### URL参数解析

**qrData参数（URL解码后）：**
```json
{
    "dev": "00900356",
    "game": "1005",
    "playin": "528930",
    "playout": "531090",
    "chk": 12131,
    "In": "0",
    "Out": "0",
    "enable": "t"
}
```

**其他参数：**
- `from_qr=1`: 标识来自二维码
- `dialog=1`: 标识在弹窗中打开

## 实现逻辑

### 1. 页面加载检测

```javascript
$(document).ready(function() {
    // 检查URL参数中是否有二维码数据
    var urlParams = new URLSearchParams(window.location.search);
    var qrDataParam = urlParams.get('qrData');
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1' && qrDataParam) {
        // 处理二维码数据
        processUrlQrData(qrDataParam);
    }
});
```

### 2. 数据解析和填充

```javascript
function processUrlQrData(qrDataParam) {
    try {
        // 解析二维码数据
        var qrData = JSON.parse(decodeURIComponent(qrDataParam));
        console.log('从URL解析的二维码数据：', qrData);
        
        // 延迟处理，确保页面完全加载
        setTimeout(function() {
            if (typeof window.fillQrCodeData === 'function') {
                window.fillQrCodeData(qrData);
            }
        }, 500);
        
    } catch (e) {
        console.error('解析URL中的二维码数据失败：', e);
        Toastr.error('二维码数据格式错误');
    }
}
```

### 3. 全局数据填充函数

```javascript
window.fillQrCodeData = function(qrData) {
    // 字段映射
    var fieldMapping = {
        'dev': 'g_serial_number',
        'game': 'g_game_id',
        'playin': 'g_total_plays',
        'playout': 'g_total_wins',
        'chk': 'g_chk',
        'In': 'g_in',
        'Out': 'g_out',
        'enable': 'g_enable'
    };
    
    // 填充表单字段
    for (var key in qrData) {
        var mappedField = fieldMapping[key] || key;
        var fieldId = '#c-' + mappedField;
        
        if ($(fieldId).length > 0) {
            if (mappedField === 'g_game_id') {
                setSelectpageValue(fieldId, qrData[key]);
            } else {
                $(fieldId).val(qrData[key]);
            }
        }
    }
    
    // 自动验证设备
    if (qrData.dev) {
        validateDevice(qrData.dev, function(deviceData) {
            Toastr.success('设备验证成功，数据已填充');
        });
    }
};
```

## 处理流程

### 1. URL访问
用户通过包含 qrData 参数的URL访问页面

### 2. 参数检测
页面加载时检测 `from_qr=1` 和 `qrData` 参数

### 3. 数据解析
- URL解码 qrData 参数
- JSON解析获取二维码数据对象

### 4. 表单填充
- 根据字段映射填充表单
- 设置总玩次数(528930)、总赢次数(531090)等

### 5. 设备验证
- 根据设备序列号(00900356)验证设备
- 获取设备ID并设置到表单

### 6. 完成处理
- 显示成功提示
- 用户可以继续编辑或提交

## 字段映射表

| 二维码字段 | 表单字段 | 示例值 | 说明 |
|-----------|---------|--------|------|
| dev | g_serial_number | "00900356" | 设备序列号 |
| game | g_game_id | "1005" | 游戏ID |
| playin | g_total_plays | "528930" | 总玩次数 |
| playout | g_total_wins | "531090" | 总赢次数 |
| chk | g_chk | 12131 | 校验码 |
| In | g_in | "0" | 输入值 |
| Out | g_out | "0" | 输出值 |
| enable | g_enable | "t" | 启用状态 |

## 错误处理

### 1. JSON解析失败
```javascript
catch (e) {
    console.error('解析URL中的二维码数据失败：', e);
    Toastr.error('二维码数据格式错误');
}
```

### 2. 函数未找到
```javascript
if (typeof window.fillQrCodeData === 'function') {
    window.fillQrCodeData(qrData);
} else {
    console.error('fillQrCodeData 函数未找到');
}
```

### 3. 设备验证失败
通过 validateDevice 函数的错误处理机制

## 测试验证

### 1. 直接访问URL
复制完整的URL到浏览器地址栏访问

### 2. 检查表单填充
确认以下字段是否正确填充：
- 设备序列号：00900356
- 游戏ID：1005
- 总玩次数：528930
- 总赢次数：531090

### 3. 检查设备验证
确认设备验证成功，g_device_id 字段有值

### 4. 控制台检查
查看浏览器控制台的日志输出：
```
从URL解析的二维码数据： {dev: "00900356", game: "1005", ...}
设置字段： #c-g_serial_number = 00900356
设置字段： #c-g_total_plays = 528930
...
```

## 使用场景

1. **二维码生成链接**：二维码中包含完整的URL
2. **外部系统跳转**：其他系统通过URL跳转到添加页面
3. **分享链接**：用户可以分享包含数据的链接
4. **批量处理**：通过程序生成多个URL进行批量操作

## 注意事项

1. **URL长度限制**：确保URL不超过浏览器限制
2. **特殊字符编码**：确保JSON数据正确编码
3. **安全性**：验证数据来源和格式
4. **兼容性**：确保在不同浏览器中正常工作

现在通过URL访问时，页面会自动解析 qrData 参数并填充表单数据！
