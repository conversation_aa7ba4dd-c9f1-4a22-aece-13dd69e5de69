# 摄像头黑屏问题解决方案

## 问题分析

### 常见现象
- 摄像头画面出现一瞬间，然后立即黑屏
- 有时能看到画面，有时完全黑屏
- 切换摄像头时出现黑屏

### 根本原因

#### 1. 摄像头资源冲突
```
多个MediaStream同时创建 → 
浏览器限制同时访问 → 
后创建的流抢占资源 → 
前面的流被强制停止 → 
画面黑屏
```

#### 2. 权限管理问题
```
权限请求冲突 → 
临时权限被撤销 → 
摄像头访问被阻止 → 
画面消失
```

#### 3. 设备切换问题
```
从前置切换到后置 → 
旧流没有正确释放 → 
新流创建失败 → 
黑屏状态
```

## 解决方案

### 1. 资源清理机制

```javascript
// 清理现有的摄像头流
function cleanupExistingStreams() {
    // 停止所有活动的媒体流
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        // 获取所有活动的媒体流轨道并停止
        if (window.currentMediaStream) {
            window.currentMediaStream.getTracks().forEach(track => {
                track.stop();
                console.log('停止现有摄像头流');
            });
            window.currentMediaStream = null;
        }
    }
}
```

**关键点：**
- 在创建新流之前，先停止所有现有流
- 使用全局变量跟踪当前活动的流
- 确保每个轨道都被正确停止

### 2. 权限预检查

```javascript
// 先请求权限后再获取设备列表
navigator.mediaDevices.getUserMedia({ video: true })
    .then(function(stream) {
        // 立即停止这个测试流
        stream.getTracks().forEach(track => track.stop());
        
        // 现在获取设备列表
        return navigator.mediaDevices.enumerateDevices();
    })
    .then(function(devices) {
        // 处理设备列表
    })
    .catch(function(error) {
        console.log('摄像头权限或设备获取失败：', error);
    });
```

**关键点：**
- 先获取权限，确保有访问摄像头的权限
- 立即停止测试流，避免资源占用
- 权限确认后再进行设备枚举

### 3. 智能切换机制

```javascript
// 只有在需要切换时才切换
if (targetIndex !== originalIndex) {
    console.log('切换摄像头从', originalIndex, '到', targetIndex);
    cameraSelect.selectedIndex = targetIndex;
    
    // 触发选择变化事件
    var changeEvent = new Event('change', { bubbles: true });
    cameraSelect.dispatchEvent(changeEvent);
    
    // 等待切换完成后启动
    setTimeout(function() {
        startCameraWithRetry();
    }, 1000);
} else {
    console.log('已经选择了正确的摄像头，直接启动');
    // 直接启动
    setTimeout(function() {
        startCameraWithRetry();
    }, 500);
}
```

**关键点：**
- 检查当前选择，避免不必要的切换
- 切换后等待更长时间再启动
- 记录切换过程，便于调试

### 4. 重试机制

```javascript
// 带重试的启动摄像头
function startCameraWithRetry() {
    var startAttempts = 0;
    var maxStartAttempts = 3;
    
    function tryStart() {
        startAttempts++;
        var startButton = document.getElementById('html5-qrcode-button-camera-start');
        
        if (startButton && startButton.style.display !== 'none') {
            startButton.click();
            console.log('尝试启动摄像头，第', startAttempts, '次');
            
            // 检查启动是否成功
            setTimeout(function() {
                var video = document.querySelector('#qr-reader video');
                if (!video || video.videoWidth === 0) {
                    if (startAttempts < maxStartAttempts) {
                        console.log('摄像头启动失败，重试...');
                        setTimeout(tryStart, 1000);
                    } else {
                        console.log('摄像头启动失败，已达到最大重试次数');
                    }
                } else {
                    console.log('摄像头启动成功');
                }
            }, 2000);
        }
    }
    
    tryStart();
}
```

**关键点：**
- 检查video元素的videoWidth来判断是否成功
- 失败时自动重试，最多3次
- 每次重试间隔1秒，给设备时间恢复

### 5. 状态监控

```javascript
// 检查启动是否成功
setTimeout(function() {
    var video = document.querySelector('#qr-reader video');
    if (!video || video.videoWidth === 0) {
        // 启动失败
        console.log('摄像头启动失败，重试...');
    } else {
        // 启动成功
        console.log('摄像头启动成功，分辨率：', video.videoWidth, 'x', video.videoHeight);
    }
}, 2000);
```

**关键点：**
- 使用video.videoWidth判断是否有画面
- videoWidth > 0 表示有有效画面
- videoWidth = 0 表示黑屏或无画面

## 时序优化

### 1. 启动时序
```
清理现有流: 0ms
权限检查: +100ms
设备枚举: +200ms
下拉框等待: +1000ms
摄像头切换: +1500ms
启动摄像头: +2500ms
状态检查: +4500ms
```

### 2. 关键延迟
- **切换后等待**: 1000ms（给设备时间切换）
- **启动前等待**: 500ms（确保下拉框状态稳定）
- **状态检查延迟**: 2000ms（等待画面稳定）
- **重试间隔**: 1000ms（避免频繁重试）

## 调试信息

### 1. 控制台输出
```
停止现有摄像头流
可用摄像头： [设备列表]
找到目标后置摄像头： Back Camera
切换摄像头从 0 到 1
尝试启动摄像头，第 1 次
摄像头启动成功，分辨率： 1280 x 720
```

### 2. 状态检查
```javascript
// 手动检查摄像头状态
function checkCameraStatus() {
    var video = document.querySelector('#qr-reader video');
    if (video) {
        console.log('Video元素存在');
        console.log('视频尺寸：', video.videoWidth, 'x', video.videoHeight);
        console.log('视频状态：', video.readyState);
        console.log('是否暂停：', video.paused);
    } else {
        console.log('未找到Video元素');
    }
}

// 2秒后检查状态
setTimeout(checkCameraStatus, 2000);
```

### 3. 流状态监控
```javascript
// 监控媒体流状态
if (window.currentMediaStream) {
    window.currentMediaStream.getTracks().forEach(track => {
        console.log('轨道状态：', track.kind, track.readyState, track.enabled);
    });
}
```

## 预防措施

### 1. 避免并发
- 同时只创建一个摄像头流
- 切换前先停止当前流
- 使用队列管理摄像头操作

### 2. 权限管理
- 启动前确认权限状态
- 处理权限被拒绝的情况
- 提供权限恢复指导

### 3. 错误恢复
- 自动重试机制
- 降级到默认摄像头
- 用户手动重启选项

### 4. 用户体验
- 显示加载状态
- 提供错误提示
- 允许手动切换摄像头

现在摄像头应该不会出现黑屏问题了！
