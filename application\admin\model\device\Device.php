<?php

namespace app\admin\model\device;

use think\Model;


class Device extends Model
{

    

    // 表名
    protected $name = 'device';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'g_time_text',
        'g_status_text',
        'g_createtime_text',
        'g_updatetime_text',
        'g_deletetime_text'
    ];
    

    
    public function getGStatusList()
    {
        return ['normal' => __('Normal'), 'locked' => __('Locked')];
    }


    public function getGTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['g_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getGStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['g_status'] ?? '');
        $list = $this->getGStatusList();
        return $list[$value] ?? '';
    }


    public function getGCreatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['g_createtime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getGUpdatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['g_updatetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getGDeletetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['g_deletetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setGTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setGCreatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setGUpdatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setGDeletetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
