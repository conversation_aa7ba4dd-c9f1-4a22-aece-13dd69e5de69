<?php

namespace app\admin\model;

use think\Model;


class Game extends Model
{





    // 表名
    protected $name = 'game';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'game_type_text',
        'mode_config_array',
        'wave_config_array',
        'rate_config_array',
        'back_config_array',
        'limit_config_array',
        'time_config_array',
        'enable_config_array'
    ];



    public function getGameTypeList()
    {
        return ['Fishing' => __('Game_type Fishing'), 'Coin_Pusher' => __('Game_type Coin_Pusher'), 'Slots' => __('Game_type Slots')];
    }


    public function getGameTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['game_type'] ?? '');
        $list = $this->getGameTypeList();
        return $list[$value] ?? '';
    }

    /**
     * 获取算法模式控制参数配置数组
     */
    public function getModeConfigArrayAttr($value, $data)
    {
        $value = $data['mode_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取算法波动参数配置数组
     */
    public function getWaveConfigArrayAttr($value, $data)
    {
        $value = $data['wave_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取游戏几率配置数组
     */
    public function getRateConfigArrayAttr($value, $data)
    {
        $value = $data['rate_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取备用参数配置数组
     */
    public function getBackConfigArrayAttr($value, $data)
    {
        $value = $data['back_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取爆机数值配置数组
     */
    public function getLimitConfigArrayAttr($value, $data)
    {
        $value = $data['limit_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取游戏局数控制配置数组
     */
    public function getTimeConfigArrayAttr($value, $data)
    {
        $value = $data['time_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取启用参数配置数组
     */
    public function getEnableConfigArrayAttr($value, $data)
    {
        $value = $data['enable_config'] ?? '';
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 获取启用参数配置
     */
    public function getEnableConfigAttr($value, $data)
    {
        return $data['enable_config'] ?? '';
    }

    /**
     * 设置算法模式控制参数配置
     */
    public function setModeConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置算法波动参数配置
     */
    public function setWaveConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置游戏几率配置
     */
    public function setRateConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置备用参数配置
     */
    public function setBackConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置爆机数值配置
     */
    public function setLimitConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置游戏局数控制配置
     */
    public function setTimeConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 设置启用参数配置
     */
    public function setEnableConfigArrayAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }
}
