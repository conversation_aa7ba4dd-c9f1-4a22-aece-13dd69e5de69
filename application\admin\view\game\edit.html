<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_code" data-rule="required" class="form-control" name="row[game_code]" type="text" value="{$row.game_code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_name" data-rule="required" class="form-control" name="row[game_name]" type="text" value="{$row.game_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-game_type" data-rule="required" class="form-control selectpicker" name="row[game_type]">
                {foreach name="gameTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.game_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.created_at}
        </div>
    </div>

    <!-- 算法模式控制参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mode_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach name="mode_config_array" item="config" key="index"}
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[mode_config][{$index}][key]" value="{$config.key}"></td>
                                                <td><input type="text" class="form-control mode-item" data-index="{$index}" name="row[mode_config][{$index}][value]" value="{$config.value}"></td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 算法波动参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wave_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach name="wave_config_array" item="config" key="index"}
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[wave_config][{$index}][key]" value="{$config.key}"></td>
                                                <td><input type="text" class="form-control wave-item" data-index="{$index}" name="row[wave_config][{$index}][value]" value="{$config.value}"></td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 游戏几率配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rate_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>值</th>
                                                <th>描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach name="rate_config_array" item="config" key="index"}
                                            <tr>
                                                <td><input type="text" class="form-control" name="row[rate_config][{$index}][key]" value="{$config.key}"></td>
                                                <td><input type="text" class="form-control rate-item" data-index="{$index}" name="row[rate_config][{$index}][value]" value="{$config.value|default='默认值'}"></td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 备用参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Back_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach name="back_config_array" item="config" key="index"}
                        <tr>
                            <td><input type="text" class="form-control" name="row[back_config][{$index}][key]" value="{$config.key}"></td>
                            <td><input type="text" class="form-control back-item" data-index="{$index}" name="row[back_config][{$index}][value]" value="{$config.value|default='默认值'}"></td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select class="form-control" name="row[enable_type]">
                <option value="1" <?php if($row['enable_type'] == 1) echo 'selected'; ?>>{:__('Used_for_parameter_enable')}</option>
                <option value="2" <?php if($row['enable_type'] == 2) echo 'selected'; ?>>{:__('Used_for_setting_coin_score')}</option>
            </select>
        </div>
    </div>



    <!-- 启用参数配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach name="enable_config_array" item="config" key="index"}
                        <tr>
                            <td><input type="text" class="form-control" name="row[enable_config]['{$index}'][key]" value="{$config.key}"></td>
                            <td><input type="text" class="form-control enable-item" data-index="{$index}" name="row[enable_config]['{$index}'][value]" value="{$config.value|default='默认值'}"></td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 爆机数值配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Limit_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="row[limit_config][range]" value="{$row.limit_config_array.range|default='-32876000-328750000'}">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="row[limit_config][description]" value="{$row.limit_config_array.description|default='爆机数值，0为不控。'}">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 游戏局数控制配置 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Time_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{:__('Config_range')}</th>
                            <th>{:__('Config_description')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="row[time_config][range]" value="{$row.time_config_array.range|default='0-32875000'}">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="row[time_config][description]" value="{$row.time_config_array.description|default='游戏局数控制，0为不控。'}">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
<script>
    // 表单提交前处理数据
    $(document).ready(function() {
        $('#edit-form').on('submit', function() {
            // 所有配置项已经在表单中设置好了

            return true;
        });
    });
</script>
</form>
