# 后端二维码数据处理实现

## 概述

将 g_id 补充逻辑从前端 JavaScript 移动到后端控制器中处理，提高效率和安全性。

## 实现位置

**文件：** `application/admin/controller/device/DeviceRecord.php`
**方法：** `add()` 方法的 GET 请求处理部分

## 核心实现

### 1. 二维码数据检测和解析

```php
// 处理二维码数据
if (isset($params['qrData']) && $params['from_qr'] == '1') {
    try {
        $qrData = json_decode(urldecode($params['qrData']), true);
        if ($qrData) {
            // 处理二维码数据
        }
    } catch (Exception $e) {
        $this->error('二维码数据格式错误');
    }
}
```

### 2. 字段映射

```php
// 字段映射
$fieldMapping = [
    'device_id' => 'g_device_id',
    'dev' => 'g_serial_number',
    'rid' => 'g_rid',
    'game' => 'g_game_id',
    'playin' => 'g_total_plays',
    'playout' => 'g_total_wins',
    'chk' => 'g_chk',
    'In' => 'g_in',
    'Out' => 'g_out',
    'enable' => 'g_enable'
];

// 映射二维码数据到参数
foreach ($qrData as $key => $value) {
    if (isset($fieldMapping[$key])) {
        $params[$fieldMapping[$key]] = $value;
    } else {
        $params[$key] = $value;
    }
}
```

### 3. g_id 自动补充

```php
// 如果有设备序列号但没有设备ID，根据序列号获取设备ID
if (!empty($params['g_serial_number']) && empty($params['g_device_id'])) {
    $deviceModel = new \app\admin\model\device\Device;
    $device = $deviceModel->where('g_serial_number', $params['g_serial_number'])->find();
    
    if ($device) {
        // 检查权限：group_id=7 的用户只能操作自己的设备
        if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
            $this->error('您没有权限操作此设备');
        }
        
        // 补充设备ID和其他设备信息
        $params['g_device_id'] = $device['g_id'];
        $params['g_game_id'] = $device['g_game_id'];
        $params['g_total_plays'] = $device['g_total_plays'];
        $params['g_total_wins'] = $device['g_total_wins'];
        
        // 补充其他设备配置信息
        $params['g_mode'] = $device['g_mode'];
        $params['g_wave'] = $device['g_wave'];
        // ... 其他字段
    } else {
        $this->error('设备不存在，请先添加设备');
    }
}
```

## 处理流程

### 1. URL 参数格式

```
device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22...%7D&from_qr=1
```

### 2. 后端处理步骤

1. **检测二维码标识**：检查 `from_qr=1` 和 `qrData` 参数
2. **解析JSON数据**：`json_decode(urldecode($params['qrData']), true)`
3. **字段映射**：将二维码字段映射到表单字段
4. **设备查询**：根据 `g_serial_number` 查询设备信息
5. **权限检查**：验证用户是否有权限操作该设备
6. **数据补充**：补充 `g_device_id` 和其他设备配置
7. **参数合并**：与默认参数合并后传递给模板

### 3. 前端简化

前端只需要：
- 检测 `from_qr=1` 标识
- 设置设备验证状态
- 显示成功提示

```javascript
$(document).ready(function() {
    var urlParams = new URLSearchParams(window.location.search);
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1') {
        // 后端已经处理了数据填充
        deviceValidated = true;
        $('#device-status').html('<span class="text-success">✓ 设备验证成功（来自二维码）</span>');
        
        setTimeout(function() {
            Toastr.success('二维码数据已自动填充');
        }, 500);
    }
});
```

## 优势

### 1. 性能提升
- 减少前端 AJAX 请求
- 避免异步处理的复杂性
- 页面加载时数据已准备就绪

### 2. 安全性增强
- 服务端验证设备权限
- 统一的错误处理
- 避免前端数据篡改

### 3. 代码简化
- 前端逻辑大幅简化
- 减少 JavaScript 代码量
- 统一的数据处理流程

### 4. 维护性提升
- 业务逻辑集中在后端
- 便于调试和日志记录
- 易于扩展新字段

## 错误处理

### 1. 数据格式错误
```php
catch (Exception $e) {
    $this->error('二维码数据格式错误');
}
```

### 2. 设备不存在
```php
if (!$device) {
    $this->error('设备不存在，请先添加设备');
}
```

### 3. 权限不足
```php
if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
    $this->error('您没有权限操作此设备');
}
```

## 支持的二维码字段

| 二维码字段 | 表单字段 | 说明 |
|-----------|---------|------|
| device_id | g_device_id | 设备ID |
| dev | g_serial_number | 设备序列号 |
| rid | g_rid | 设备RID |
| game | g_game_id | 游戏ID |
| playin | g_total_plays | 总玩次数 |
| playout | g_total_wins | 总赢次数 |
| chk | g_chk | 校验码 |
| In | g_in | 输入值 |
| Out | g_out | 输出值 |
| enable | g_enable | 启用状态 |

## 自动补充的设备信息

当根据 `g_serial_number` 找到设备后，会自动补充以下信息：

- `g_device_id` - 设备ID
- `g_game_id` - 游戏ID
- `g_total_plays` - 总玩次数
- `g_total_wins` - 总赢次数
- `g_mode` - 设备模式
- `g_wave` - 波动值
- `g_rate` - 费率
- `g_back` - 返还值
- `g_limit` - 限制值
- `g_time` - 时间设置
- `g_enable` - 启用状态
- `g_enable_type` - 启用类型
- 以及其他所有设备配置字段

## 测试验证

### 1. 正常流程测试
1. 扫码生成包含 `dev` 字段的二维码
2. 跳转到添加页面
3. 检查页面是否自动填充了所有字段
4. 验证 `g_device_id` 是否正确设置

### 2. 权限测试
1. 使用 group_id=7 的用户
2. 扫描其他商户的设备二维码
3. 应该显示"您没有权限操作此设备"错误

### 3. 错误处理测试
1. 扫描不存在设备的二维码
2. 应该显示"设备不存在，请先添加设备"错误
3. 传递格式错误的 qrData 参数
4. 应该显示"二维码数据格式错误"错误
