-- 添加API响应信息字段到设备打码记录表
ALTER TABLE `gc_device_record` 
ADD COLUMN `g_api_code` int(11) DEFAULT NULL COMMENT 'API响应代码',
ADD COLUMN `g_api_msg` varchar(255) DEFAULT NULL COMMENT 'API响应消息',
ADD COLUMN `g_api_state` varchar(50) DEFAULT NULL COMMENT 'API响应状态',
ADD COLUMN `g_api_time` int(11) DEFAULT NULL COMMENT 'API调用时间';

-- 添加失败状态到g_status枚举
ALTER TABLE `gc_device_record` 
MODIFY COLUMN `g_status` enum('pending','approved','failed') DEFAULT 'pending' COMMENT '状态:pending=审核中,approved=已审核,failed=失败';
