define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'layer'], function ($, undefined, Backend, Table, Form, Layer) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'device/device_record/index' + location.search,
                    add_url: 'device/device_record/add',
                    edit_url: 'device/device_record/edit',
                    del_url: 'device/device_record/del',
                    multi_url: 'device/device_record/multi',
                    import_url: 'device/device_record/import',
                    table: 'device_record',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'g_device_id', title: __('G_device_id')},
                        {field: 'g_serial_number', title: __('G_serial_number'), operate: 'LIKE'},
                        {field: 'g_game_id', title: __('G_game_id')},
                        {field: 'g_total_plays', title: __('G_total_plays')},
                        {field: 'g_total_wins', title: __('G_total_wins')},
                        {field: 'g_status', title: __('G_status'), searchList: {"pending":__('G_status pending'),"approved":__('G_status approved')}, formatter: Table.api.formatter.status},
                        {field: 'g_createtime', title: __('G_createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_updatetime', title: __('G_updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_admin_id', title: __('G_admin_id')},
                        {field: 'g_merchant_id', title: __('G_merchant_id')},
                        {field: 'g_operator_id', title: __('G_operator_id')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            Controller.api.initQrScanner();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            initQrScanner: function() {
                // 检查是否在添加页面
                if ($("form[role=form]").length <= 0) {
                    return;
                }

                // 如果扫码按钮已存在，不重复创建
                if ($("#btn-scan").length > 0) {
                    return;
                }

                // 在表单顶部添加扫码按钮
                if ($("#scan-button-container").length <= 0) {
                    var scanButtonHtml = '<div class="form-group" id="scan-button-container">'+
                        '<label class="control-label col-xs-12 col-sm-2">扫码填充:</label>'+
                        '<div class="col-xs-12 col-sm-8">'+
                        '<button type="button" class="btn btn-info btn-scan" id="btn-scan"><i class="fa fa-qrcode"></i> 扫描二维码</button>'+
                        '</div>'+
                        '</div>';
                    $("form[role=form]").prepend(scanButtonHtml);
                }

                // 如果模态框不存在，添加模态框
                if ($("#scanModal").length <= 0) {
                    var modalHtml = '<div class="modal fade" id="scanModal" tabindex="-1" role="dialog" aria-labelledby="scanModalLabel">'+
                        '<div class="modal-dialog" role="document">'+
                        '<div class="modal-content">'+
                        '<div class="modal-header">'+
                        '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'+
                        '<h4 class="modal-title" id="scanModalLabel">扫描二维码</h4>'+
                        '</div>'+
                        '<div class="modal-body">'+
                        '<div id="reader" style="width:100%;"></div>'+
                        '<div class="text-center" style="margin-top:10px;">'+
                        '<p class="text-muted">请将二维码对准摄像头，系统会自动识别并填充表单</p>'+
                        '<p class="text-muted">二维码格式示例: {"dev":"SN12345","game":"FISH001","playin":1000,"playout":500}</p>'+
                        '</div>'+
                        '</div>'+
                        '<div class="modal-footer">'+
                        '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>'+
                        '</div>'+
                        '</div>'+
                        '</div>'+
                        '</div>';
                    $("body").append(modalHtml);
                }

                // 扫码器实例
                var html5QrcodeScanner = null;

                // 加载扫码库
                function loadQrCodeLibrary(callback) {
                    // 如果已经加载，直接调用回调
                    if (typeof window.Html5QrcodeScanner !== 'undefined') {
                        console.log('Html5QrcodeScanner 库已加载');
                        callback();
                        return;
                    }

                    console.log('开始加载 Html5QrcodeScanner 库');
                    var script = document.createElement('script');
                    script.src = "https://cdn.bootcdn.net/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js";
                    script.async = true;

                    script.onload = function() {
                        console.log('Html5QrcodeScanner 库加载成功');
                        // 确保库已经加载完成
                        setTimeout(function() {
                            if (typeof window.Html5QrcodeScanner !== 'undefined') {
                                callback();
                            } else {
                                console.error('Html5QrcodeScanner 库加载完成但未定义');
                                Layer.msg('扫码库加载失败，请刷新页面后重试', {icon: 2});
                            }
                        }, 500); // 等待500毫秒确保库已加载完成
                    };

                    script.onerror = function() {
                        console.error('Html5QrcodeScanner 库加载失败');
                        Layer.msg('扫码库加载失败，请检查网络连接后重试', {icon: 2});
                    };

                    document.head.appendChild(script);
                }

                // 点击扫码按钮
                $("#btn-scan").on("click", function() {
                    // 检查浏览器是否支持摄像头访问
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        Layer.msg('您的浏览器不支持摄像头访问，请使用现代浏览器', {icon: 2});
                        return;
                    }

                    // 加载扫码库并打开模态框
                    loadQrCodeLibrary(function() {
                        $("#scanModal").modal('show');
                    });
                });

                // 模态框显示时初始化扫码器
                $('#scanModal').on('shown.bs.modal', function() {
                    // 如果扫码器实例已存在，先清除
                    if (html5QrcodeScanner) {
                        html5QrcodeScanner.clear();
                        html5QrcodeScanner = null;
                    }

                    // 初始化扫码器
                    try {
                        // 处理扫描成功的回调函数
                        function onScanSuccess(decodedText, decodedResult) {
                            console.log(`扫描成功: ${decodedText}`, decodedResult);

                            try {
                                // 解析JSON数据
                                var qrData = JSON.parse(decodedText);

                                // 填充表单
                                if (qrData.dev) {
                                    $("#c-g_device_id").val(qrData.dev);
                                    $("#c-g_serial_number").val(qrData.dev);
                                }
                                if (qrData.game) {
                                    $("#c-g_game_id").val(qrData.game);
                                    // 如果使用selectpage，触发变化事件
                                    $("#c-g_game_id").trigger('change');
                                }
                                if (qrData.playin) {
                                    $("#c-g_total_plays").val(qrData.playin);
                                }
                                if (qrData.playout) {
                                    $("#c-g_total_wins").val(qrData.playout);
                                }

                                // 关闭模态框
                                setTimeout(function() {
                                    $("#scanModal").modal('hide');
                                }, 1000);

                                // 停止扫码器
                                if (html5QrcodeScanner) {
                                    html5QrcodeScanner.clear();
                                }
                            } catch (e) {
                                console.error('解析JSON数据失败:', e);
                                Layer.msg('解析失败! 请确保二维码包含有效的JSON数据', {icon: 2});
                            }
                        }

                        // 处理扫描错误的回调函数
                        function onScanError(errorMessage) {
                            // 错误处理，一般不需要显示给用户
                            console.log('扫描错误:', errorMessage);
                        }

                        // 创建扫码器实例
                        html5QrcodeScanner = new Html5QrcodeScanner(
                            "reader",
                            {
                                fps: 10,
                                qrbox: 250,
                                rememberLastUsedCamera: true,
                                // 默认使用后置摄像头
                                defaultDeviceId: 'environment',
                                // 支持双击切换摄像头
                                showTorchButtonIfSupported: true,
                                showZoomSliderIfSupported: true
                            },
                            /* verbose= */ false);

                        // 渲染扫码器
                        html5QrcodeScanner.render(onScanSuccess, onScanError);

                        // 添加双击切换摄像头的功能
                        setTimeout(function() {
                            var readerElement = document.getElementById('reader');
                            if (readerElement) {
                                // 记录双击时间
                                var lastTapTime = 0;

                                readerElement.addEventListener('click', function(e) {
                                    var currentTime = new Date().getTime();
                                    var tapLength = currentTime - lastTapTime;

                                    // 如果是双击（小于500毫秒）
                                    if (tapLength < 500 && tapLength > 0) {
                                        console.log('双击检测到，尝试切换摄像头');

                                        // 尝试切换摄像头
                                        try {
                                            // 获取切换摄像头的按钮
                                            var cameraSelectionContainer = readerElement.querySelector('#html5-qrcode-select-camera');
                                            if (cameraSelectionContainer) {
                                                var cameraSelection = cameraSelectionContainer.querySelector('select');
                                                if (cameraSelection && cameraSelection.options.length > 1) {
                                                    // 选择下一个摄像头
                                                    var currentIndex = cameraSelection.selectedIndex;
                                                    var nextIndex = (currentIndex + 1) % cameraSelection.options.length;
                                                    cameraSelection.selectedIndex = nextIndex;

                                                    // 触发change事件
                                                    var event = new Event('change');
                                                    cameraSelection.dispatchEvent(event);

                                                    Layer.msg('切换到摄像头: ' + cameraSelection.options[nextIndex].text, {icon: 1});
                                                }
                                            }
                                        } catch (err) {
                                            console.error('切换摄像头时出错:', err);
                                        }
                                    }

                                    lastTapTime = currentTime;
                                });
                            }
                        }, 1000); // 等待扫码器渲染完成

                    } catch (error) {
                        console.error('初始化扫码器时出错:', error);
                        Layer.msg('初始化扫码器时出错，请刷新页面后重试', {icon: 2});
                    }
                });

                // 模态框关闭时停止扫码
                $('#scanModal').on('hidden.bs.modal', function() {
                    // 停止扫码器
                    if (html5QrcodeScanner) {
                        try {
                            html5QrcodeScanner.clear();
                            html5QrcodeScanner = null;
                        } catch (error) {
                            console.error('停止扫码器时出错:', error);
                        }
                    }
                });
            }
        }
    };
    return Controller;
});
