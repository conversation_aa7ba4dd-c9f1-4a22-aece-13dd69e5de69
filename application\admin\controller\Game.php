<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 游戏管理
 *
 * @icon fa fa-circle-o
 */
class Game extends Backend
{

    /**
     * Game模型对象
     * @var \app\admin\model\Game
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Game;
        $this->view->assign("gameTypeList", $this->model->getGameTypeList());
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 处理JSON配置字段
                $configFields = ['mode_config', 'wave_config', 'rate_config', 'back_config', 'limit_config', 'time_config', 'enable_config'];
                foreach ($configFields as $field) {
                    if (isset($params[$field]) && is_array($params[$field])) {
                        // 处理新的表单格式
                        $values = [];
                        foreach ($params[$field] as $item) {
                            if (isset($item['key']) && isset($item['value'])) {
                                $key = $item['key'] !=='' ? $item['key'] : '';
                                $values[$key] = ['key'=>$key, 'value'=>$item['value']];
                            }
                        }

                        
                    }

                    

                    if(isset($params[$field]['description'])){
                        $values['description'] = $params[$field]['description'];
                    }
                
                    if(isset($params[$field]['range'])){
                        $values['range'] = $params[$field]['range'];
                    }

                    $params[$field] = json_encode($values, JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION | JSON_FORCE_OBJECT);
                }

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                $params['create_at'] = time();
                $result = $this->model->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($this->model->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 处理JSON配置字段
                $configFields = ['mode_config', 'wave_config', 'rate_config', 'back_config', 'limit_config', 'time_config', 'enable_config'];
                foreach ($configFields as $field) {
                    if (isset($params[$field]) && is_array($params[$field])) {
                        // 处理新的表单格式
                        $values = [];
                        foreach ($params[$field] as $item) {
                            if (isset($item['key']) && isset($item['value'])) {
                                $key = $item['key']!=='' ? $item['key'] : '';
                                $values[$key] = ['key'=>$key, 'value'=>$item['value']];
                            }
                        }
                    }

                    

                    if(isset($params[$field]['description'])){
                        $values['description'] = $params[$field]['description'];
                    }
                
                    if(isset($params[$field]['range'])){
                        $values['range'] = $params[$field]['range'];
                    }
                
                    $params[$field] = json_encode($values, JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION | JSON_FORCE_OBJECT);
                }

                $params['update_time'] = time();

                $result = $row->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($row->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        $this->view->assign("mode_config_array", json_decode($row['mode_config'], true));
        $this->view->assign("wave_config_array", json_decode($row['wave_config'], true));
        $this->view->assign("rate_config_array", json_decode($row['rate_config'], true));
        $this->view->assign("back_config_array", json_decode($row['back_config'], true));
        $this->view->assign("limit_config_array", json_decode($row['limit_config'], true));
        $this->view->assign("time_config_array", json_decode($row['time_config'], true));
        $this->view->assign("enable_config_array", json_decode($row['enable_config'], true));
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
