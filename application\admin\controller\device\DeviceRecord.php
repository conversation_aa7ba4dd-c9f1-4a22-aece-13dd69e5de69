<?php

namespace app\admin\controller\device;

use app\common\controller\Backend;
use app\admin\library\DeviceApi;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 打码记录管理
 *
 * @icon fa fa-circle-o
 */
class DeviceRecord extends Backend
{

    /**
     * DeviceRecord模型对象
     * @var \app\admin\model\device\DeviceRecord
     */
    protected $model = null;

    /**
     * 是否开启数据限制
     * 对于 group_id=7 的用户，启用基于 g_merchant_id 的数据限制
     */
    protected $dataLimit = 'auth';

    /**
     * 数据限制字段
     * 对于 group_id=7 的用户，使用 g_merchant_id 字段进行限制
     */
    protected $dataLimitField = 'g_merchant_id';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\device\DeviceRecord;
        $this->view->assign("gStatusList", $this->model->getGStatusList());

        // 对于 group_id=7 的用户，启用特殊的数据权限控制
        if ($this->auth->group_id == 7) {
            $this->dataLimit = 'merchant';
            $this->dataLimitField = 'g_merchant_id';
        }
    }

    /**
     * 获取数据限制的管理员ID
     * 对于 group_id=7 的用户，返回当前用户ID用于 g_merchant_id 字段过滤
     * @return mixed
     */
    protected function getDataLimitAdminIds()
    {
        if (!$this->dataLimit) {
            return null;
        }
        if ($this->auth->isSuperAdmin()) {
            return null;
        }

        // 对于 group_id=7 的商户用户，直接限制只能查看自己的数据
        if ($this->auth->group_id == 7) {
            // 返回当前用户ID，用于 g_merchant_id 字段过滤
            return [$this->auth->id];
        }

        // 其他用户组使用默认逻辑
        $adminIds = [];
        if (in_array($this->dataLimit, ['auth', 'personal'])) {
            $adminIds = $this->dataLimit == 'auth' ? $this->auth->getChildrenAdminIds(true) : [$this->auth->id];
        }
        return $adminIds;
    }

    /**
     * 获取游戏配置
     * @param string $gameCode 游戏代号
     * @return array
     */
    protected function getGameConfig($gameCode)
    {
        $gameModel = new \app\admin\model\Game;
        $game = $gameModel->where('game_code', $gameCode)->find();
        if (!$game) {
            return [];
        }

        // 直接返回游戏配置数据
        return [
            'mode_config' => $game->mode_config ? json_decode($game->mode_config, true) : [],
            'wave_config' => $game->wave_config ? json_decode($game->wave_config, true) : [],
            'rate_config' => $game->rate_config ? json_decode($game->rate_config, true) : [],
            'back_config' => $game->back_config ? json_decode($game->back_config, true) : [],
            'limit_config' => $game->limit_config ? json_decode($game->limit_config, true) : [],
            'time_config' => $game->time_config ? json_decode($game->time_config, true) : [],
            'enable_config' => $game->enable_config ? json_decode($game->enable_config, true) : []
        ];
    }



    /**
     * 获取游戏配置（AJAX）
     */
    public function get_game_config()
    {
        $gameId = $this->request->get('game_id');
        if (!$gameId) {
            $this->error(__('Parameter %s can not be empty', 'game_id'));
        }

        $config = $this->getGameConfig($gameId);
        if (empty($config)) {
            $this->error(__('Game configuration not found'));
        }

        $this->success('', null, $config);
    }

    /**
     * 验证设备是否存在（AJAX）
     */
    public function validate_device()
    {
        $serialNumber = $this->request->get('serial_number');
        if (!$serialNumber) {
            $this->error(__('Parameter %s can not be empty', 'serial_number'));
        }

        $deviceModel = new \app\admin\model\device\Device;
        $device = $deviceModel->where('g_serial_number', $serialNumber)->find();

        if (!$device) {
            $this->error('设备不存在，请先添加设备');
        }

        // 检查权限：group_id=7 的用户只能操作自己的设备
        if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
            $this->error('您没有权限操作此设备');
        }

        $this->success('设备验证成功', null, [
            'g_id' => $device['g_id'],
            'g_serial_number' => $device['g_serial_number'],
            'g_game_id' => $device['g_game_id'],
            'g_total_plays' => $device['g_total_plays'],
            'g_total_wins' => $device['g_total_wins'],
            'g_mode' => $device['g_mode'],
            'g_wave' => $device['g_wave'],
            'g_rate' => $device['g_rate'],
            'g_back' => $device['g_back'],
            'g_limit' => $device['g_limit'],
            'g_time' => $device['g_time'],
            'g_enable' => $device['g_enable'],
            'g_enable_type' => $device['g_enable_type'],
            'g_enable_modify_mode' => $device['g_enable_modify_mode'],
            'g_enable_adjust_rate' => $device['g_enable_adjust_rate'],
            'g_enable_clear_current_account' => $device['g_enable_clear_current_account'],
            'g_enable_clear_total_account' => $device['g_enable_clear_total_account'],
            'g_accept_modify_mode' => $device['g_accept_modify_mode'],
            'g_accept_adjust_rate' => $device['g_accept_adjust_rate'],
            'g_clear_current_account' => $device['g_clear_current_account'],
            'g_clear_total_account' => $device['g_clear_total_account'],
            'g_cannon_value' => $device['g_cannon_value'],
            'g_fish_odds' => $device['g_fish_odds']
        ]);
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            // 获取URL中传递的参数
            $params = $this->request->get();

            // 处理二维码数据
            if (isset($params['qrData']) && $params['from_qr'] == '1') {
                try {
                    $qrData = json_decode(urldecode($params['qrData']), true);
                    if ($qrData && isset($qrData['dev'])) {
                        // 根据设备序列号获取设备信息
                        $deviceModel = new \app\admin\model\device\Device;
                        $device = $deviceModel->where('g_serial_number', $qrData['dev'])->find();

                        if ($device) {
                            // 检查权限
                            if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
                                $this->error('您没有权限操作此设备');
                            }

                            // 使用设备信息作为默认值
                            $params['g_device_id'] = $device['g_id'];
                            $params['g_serial_number'] = $device['g_serial_number'];
                            $params['g_game_id'] = $device['g_game_id'];
                            $params['g_total_plays'] = $device['g_total_plays'];
                            $params['g_total_wins'] = $device['g_total_wins'];
                            $params['g_mode'] = $device['g_mode'];
                            $params['g_wave'] = $device['g_wave'];
                            $params['g_rate'] = $device['g_rate'];
                            $params['g_back'] = $device['g_back'];
                            $params['g_limit'] = $device['g_limit'];
                            $params['g_time'] = $device['g_time'];
                            $params['g_enable'] = $device['g_enable'];
                            $params['g_enable_type'] = $device['g_enable_type'];
                            $params['g_enable_modify_mode'] = $device['g_enable_modify_mode'];
                            $params['g_enable_adjust_rate'] = $device['g_enable_adjust_rate'];
                            $params['g_enable_clear_current_account'] = $device['g_enable_clear_current_account'];
                            $params['g_enable_clear_total_account'] = $device['g_enable_clear_total_account'];
                            $params['g_accept_modify_mode'] = $device['g_accept_modify_mode'];
                            $params['g_accept_adjust_rate'] = $device['g_accept_adjust_rate'];
                            $params['g_clear_current_account'] = $device['g_clear_current_account'];
                            $params['g_clear_total_account'] = $device['g_clear_total_account'];
                            $params['g_cannon_value'] = $device['g_cannon_value'];
                            $params['g_fish_odds'] = $device['g_fish_odds'];

                            // 将二维码数据传递给前端
                            $params['qrData'] = $params['qrData'];
                        } else {
                            $this->error('设备不存在，请先添加设备');
                        }
                    }
                } catch (Exception $e) {
                    $this->error('二维码数据格式错误');
                }
            }

            // 如果有设备ID，从设备表中获取默认值
            if (!empty($params['g_device_id'])) {
                $deviceModel = new \app\admin\model\device\Device;
                $device = $deviceModel->where('g_id', $params['g_device_id'])->find();
                if ($device) {
                    // 将设备表中的字段值设置为默认值
                    $params['g_serial_number'] = $device['g_serial_number'];
                    $params['g_game_id'] = $device['g_game_id'];
                    $params['g_total_plays'] = $device['g_total_plays'];
                    $params['g_total_wins'] = $device['g_total_wins'];
                    $params['g_mode'] = $device['g_mode'];
                    $params['g_wave'] = $device['g_wave'];
                    $params['g_rate'] = $device['g_rate'];
                    $params['g_back'] = $device['g_back'];
                    $params['g_limit'] = $device['g_limit'];
                    $params['g_time'] = $device['g_time'];
                    $params['g_enable'] = $device['g_enable'];
                    $params['g_enable_type'] = $device['g_enable_type'];
                    $params['g_enable_modify_mode'] = $device['g_enable_modify_mode'];
                    $params['g_enable_adjust_rate'] = $device['g_enable_adjust_rate'];
                    $params['g_enable_clear_current_account'] = $device['g_enable_clear_current_account'];
                    $params['g_enable_clear_total_account'] = $device['g_enable_clear_total_account'];
                    $params['g_accept_modify_mode'] = $device['g_accept_modify_mode'];
                    $params['g_accept_adjust_rate'] = $device['g_accept_adjust_rate'];
                    $params['g_clear_current_account'] = $device['g_clear_current_account'];
                    $params['g_clear_total_account'] = $device['g_clear_total_account'];
                    $params['g_cannon_value'] = $device['g_cannon_value'];
                    $params['g_fish_odds'] = $device['g_fish_odds'];
                }
            }

            // 获取所有游戏列表
            $gameModel = new \app\admin\model\Game;
            $games = $gameModel->select();
            $gameList = [];
            foreach ($games as $game) {
                $gameList[$game['game_code']] = [
                    'name' => $game['game_name']
                ];
            }
            $this->view->assign('gameList', $gameList);

            // 如果有游戏代号，获取游戏配置
            if (!empty($params['g_game_id'])) {
                $gameConfig = $this->getGameConfig($params['g_game_id']);
                $this->view->assign('gameConfig', $gameConfig);
                $this->view->assign('currentGameId', $params['g_game_id']);
            }

            $this->view->assign('params', $params);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 验证设备是否存在
        if (empty($params['g_serial_number'])) {
            $this->error('设备序列号不能为空');
        }

        $deviceModel = new \app\admin\model\device\Device;
        $device = $deviceModel->where('g_serial_number', $params['g_serial_number'])->find();

        if (!$device) {
            $this->error('设备不存在，请先添加设备');
        }

        // 检查权限：group_id=7 的用户只能操作自己的设备
        if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
            $this->error('您没有权限操作此设备');
        }

        // 设置设备ID
        $params['g_device_id'] = $device['g_id'];

        // 校验游戏配置参数
        if (!empty($params['g_game_id'])) {
            $gameConfig = $this->getGameConfig($params['g_game_id']);
            if (!empty($gameConfig)) {
                // 定义需要校验的字段及其对应的配置项
                $fieldsToValidate = [
                    'g_mode' => 'mode_config',
                    'g_wave' => 'wave_config',
                    'g_rate' => 'rate_config',
                    'g_back' => 'back_config',
                    'g_limit' => 'limit_config',
                    'g_time' => 'time_config',
                    'g_enable' => 'enable_config'
                ];

                // 逐个校验字段
                foreach ($fieldsToValidate as $field => $configKey) {
                    // 如果有范围定义，则校验范围
                    if (isset($gameConfig[$configKey]['range']) && isset($params[$field])) {
                        $range = $gameConfig[$configKey]['range'];
                        if (strpos($range, '-') !== false) {
                            list($min, $max) = explode('-', $range);
                            if ($params[$field] < $min || $params[$field] > $max) {
                                $this->error(__($field) . ' ' . __('Value must be between %s and %s', [$min, $max]));
                            }
                        }
                    }

                    // 如果有值列表，则校验值是否在列表中
                    if (isset($params[$field]) && !empty($gameConfig[$configKey]) && !isset($gameConfig[$configKey]['range'])) {
                        // 如果不是范围类型，则判断是否在列表中
                        $validValues = [];
                        foreach ($gameConfig[$configKey] as $key => $value) {
                            if (is_array($value) && isset($value['key'])) {
                                $validValues[] = $value['key'];
                            } else if (is_numeric($key)) {
                                $validValues[] = $key;
                            }
                        }

                        if (!empty($validValues)) {
                            // 将参数值转换为字符串进行比较，确保0也能正确处理
                            $paramValue = (string)$params[$field];
                            $validStringValues = array_map('strval', $validValues);

                            if (!in_array($paramValue, $validStringValues)) {
                                $this->error(__($field) . ' ' . __('Value must be one of: %s', implode(', ', $validValues)));
                            }
                        }
                    }
                }
            }
        }

        // 处理复选框字段，如果未提交则设置为0
        $checkboxFields = [
            'g_enable_modify_mode', 'g_enable_adjust_rate',
            'g_enable_clear_current_account', 'g_enable_clear_total_account',
            'g_accept_modify_mode', 'g_accept_adjust_rate',
            'g_clear_current_account', 'g_clear_total_account', 'g_clear_total_account', 'g_clear_total_account','g_fish_odds','g_cannon_value'
        ];

        foreach ($checkboxFields as $field) {
            if (!isset($params[$field])) {
                $params[$field] = 0;
            }
        }

        // 根据用户角色设置状态
        $groupId = $this->auth->id ? $this->auth->group_id : 0;

        // 如果是group_id=7的用户，设置状态为审核中
        if ($groupId == 7) {
            $params['g_status'] = 'pending';
        } else {
            // 其他角色直接设置为已审核
            $params['g_status'] = 'approved';
        }

        $params['g_status'] = 'approved';  // 直接设置为已审核

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }

            $params['create_time'] = time();
            $params['update_time'] = time();

            // 设置正确的字段
            if($this->auth->group_id==7) {
                $params['g_merchant_id'] = $this->auth->id;
            }else{
                $params['g_admin_id'] = $this->auth->id;
            }

            $result = $this->model->allowField(true)->save($params);

            // 更新设备表中的相应字段
            if (!empty($params['g_device_id'])) {
                $deviceModel = new \app\admin\model\device\Device;
                $device = $deviceModel->where('g_id', $params['g_device_id'])->find();
                if ($device) {
                    $deviceData = [
                        'g_game_id' => $params['g_game_id'],
                        'g_total_plays' => $params['g_total_plays'],
                        'g_total_wins' => $params['g_total_wins'],
                        'g_mode' => $params['g_mode'],
                        'g_wave' => $params['g_wave'],
                        'g_rate' => $params['g_rate'],
                        'g_back' => $params['g_back'],
                        'g_limit' => $params['g_limit'],
                        'g_time' => $params['g_time'],
                        'g_enable' => $params['g_enable'],
                        'g_enable_modify_mode' => $params['g_enable_modify_mode'],
                        'g_enable_adjust_rate' => $params['g_enable_adjust_rate'],
                        'g_enable_clear_current_account' => $params['g_enable_clear_current_account'],
                        'g_enable_clear_total_account' => $params['g_enable_clear_total_account'],
                        'g_accept_modify_mode' => $params['g_accept_modify_mode'],
                        'g_accept_adjust_rate' => $params['g_accept_adjust_rate'],
                        'g_clear_current_account' => $params['g_clear_current_account'],
                        'g_clear_total_account' => $params['g_clear_total_account'],
                        'g_fish_odds' => $params['g_fish_odds'],
                        'g_cannon_value'=> $params['g_cannon_value'],
                        'g_updatetime' => time(),
                        'g_enable_type' => $params['g_enable_type'],
                    ];
                    $device->save($deviceData);
                }
            }

            // 如果状态为approved，发送数据到API服务器
            if ($params['g_status'] === 'approved') {
                // 获取刚刚插入的记录
                $record = $this->model->where('id', $this->model->id)->find();
                if ($record) {
                    // 发送到API服务器
                    $apiResult = $this->sendToDeviceApi($record->toArray());

                    // 如果API调用失败，返回错误信息
                    if (!isset($apiResult['code']) || $apiResult['code'] != 0) {
                        Db::rollback();
                        $this->error(__('API communication failed') . ': ' . ($apiResult['msg'] ?? 'Unknown error'));
                    }
                }
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage().''. $e->getFile() .''. $e->getLine());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }

        // 根据用户角色显示不同的成功提示
        if ($groupId == 7) {
            $this->success(__('Operation completed successfully') . '\n' . __('Please wait for approval from your supervisor before querying the record again'));
        } else {
            $this->success(__(''.substr($apiResult['msg'],strpos($apiResult['msg'],':')+1)),'','');
        }
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 获取所有游戏列表
            $gameModel = new \app\admin\model\Game;
            $games = $gameModel->select();
            $gameList = [];
            foreach ($games as $game) {
                $gameList[$game['game_code']] = [
                    'name' => $game['game_name']
                ];
            }
            $this->view->assign('gameList', $gameList);

            // 获取游戏配置
            if (!empty($row['g_game_id'])) {
                $gameConfig = $this->getGameConfig($row['g_game_id']);
                $this->view->assign('gameConfig', $gameConfig);
                $this->view->assign('currentGameId', $row['g_game_id']);
            }

            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 校验游戏配置参数
        if (!empty($params['g_game_id'])) {
            $gameConfig = $this->getGameConfig($params['g_game_id']);
            if (!empty($gameConfig)) {
                // 定义需要校验的字段及其对应的配置项
                $fieldsToValidate = [
                    'g_mode' => 'mode_config',
                    'g_wave' => 'wave_config',
                    'g_rate' => 'rate_config',
                    'g_back' => 'back_config',
                    'g_limit' => 'limit_config',
                    'g_time' => 'time_config',
                    'g_enable' => 'enable_config'
                ];

                // 逐个校验字段
                foreach ($fieldsToValidate as $field => $configKey) {
                    // 如果有范围定义，则校验范围
                    if (isset($gameConfig[$configKey]['range']) && isset($params[$field])) {
                        $range = $gameConfig[$configKey]['range'];
                        if (strpos($range, '-') !== false) {
                            list($min, $max) = explode('-', $range);
                            if ($params[$field] < $min || $params[$field] > $max) {
                                $this->error(__($field) . ' ' . __('Value must be between %s and %s', [$min, $max]));
                            }
                        }
                    }

                    // 如果有值列表，则校验值是否在列表中
                    if (isset($params[$field]) && !empty($gameConfig[$configKey]) && !isset($gameConfig[$configKey]['range'])) {
                        // 如果不是范围类型，则判断是否在列表中
                        $validValues = [];
                        foreach ($gameConfig[$configKey] as $key => $value) {
                            if (is_array($value) && isset($value['key'])) {
                                $validValues[] = $value['key'];
                            } else if (is_numeric($key)) {
                                $validValues[] = $key;
                            }
                        }

                        if (!empty($validValues)) {
                            // 将参数值转换为字符串进行比较，确保0也能正确处理
                            $paramValue = (string)$params[$field];
                            $validStringValues = array_map('strval', $validValues);

                            if (!in_array($paramValue, $validStringValues)) {
                                $this->error(__($field) . ' ' . __('Value must be one of: %s', implode(', ', $validValues)));
                            }
                        }
                    }
                }
            }
        }

        // 处理复选框字段，如果未提交则设置为0
        $checkboxFields = [
            'g_enable_modify_mode', 'g_enable_adjust_rate',
            'g_enable_clear_current_account', 'g_enable_clear_total_account',
            'g_accept_modify_mode', 'g_accept_adjust_rate',
            'g_clear_current_account', 'g_clear_total_account'
        ];

        foreach ($checkboxFields as $field) {
            if (!isset($params[$field])) {
                $params[$field] = 0;
            }
        }

        // 根据用户角色设置状态
        $groupId = $this->auth->id ? $this->auth->group_id : 0;

        // 如果是group_id=7的用户，设置状态为审核中
        if ($groupId == 7) {
            $params['g_status'] = 'pending';
        } else {
            // 其他角色直接设置为已审核
            $params['g_status'] = 'approved';
        }

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);

            // 更新设备表中的相应字段
            if (!empty($params['g_device_id'])) {
                $deviceModel = new \app\admin\model\device\Device;
                $device = $deviceModel->where('g_id', $params['g_device_id'])->find();
                if ($device) {
                    $deviceData = [
                        'g_game_id' => $params['g_game_id'],
                        'g_total_plays' => $params['g_total_plays'],
                        'g_total_wins' => $params['g_total_wins'],
                        'g_mode' => $params['g_mode'],
                        'g_wave' => $params['g_wave'],
                        'g_rate' => $params['g_rate'],
                        'g_back' => $params['g_back'],
                        'g_limit' => $params['g_limit'],
                        'g_time' => $params['g_time'],
                        'g_enable' => $params['g_enable'],
                        'g_enable_modify_mode' => $params['g_enable_modify_mode'],
                        'g_enable_adjust_rate' => $params['g_enable_adjust_rate'],
                        'g_enable_clear_current_account' => $params['g_enable_clear_current_account'],
                        'g_enable_clear_total_account' => $params['g_enable_clear_total_account'],
                        'g_updatetime' => time(),
                        'g_enable_type' => $params['g_enable_type'],
                    ];
                    $device->save($deviceData);
                }
            }

            // 如果状态为approved，发送数据到API服务器
            if ($params['g_status'] === 'approved') {
                // 获取更新后的记录
                $record = $this->model->where('id', $ids)->find();
                if ($record) {
                    // 发送到API服务器
                    $apiResult = $this->sendToDeviceApi($record->toArray());

                    // 如果API调用失败，返回错误信息
                    if (!isset($apiResult['code']) || $apiResult['code'] != 0) {
                        Db::rollback();
                        $this->error(__('API communication failed') . ': ' . ($apiResult['msg'] ?? 'Unknown error'));
                    }
                }
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }

        // 根据用户角色显示不同的成功提示
        if ($groupId == 7) {
            $this->success(__('Operation completed successfully') . '\n' . __('Please wait for approval from your supervisor before querying the record again'));
        } else {
            $this->success(__('Operation completed successfully'));
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 发送设备打码数据到API服务器
     *
     * @param array $record 设备打码记录数据
     * @return array 返回结果，包含code和msg
     */
    protected function sendToDeviceApi($record)
    {
        // 创建API通信实例
        $deviceApi = new DeviceApi();

        // 发送数据到API服务器
        $result = $deviceApi->sendDeviceRecord($record);


        // 更新记录，添加API响应信息
        $updateData = [
            'g_api_code' => isset($result['code']) ? $result['code'] : -999,
            'g_api_msg' => isset($result['msg']) ? $result['msg'] : 'Unknown error',
            'g_api_state' => isset($result['state']) ? $result['state'] : '',
            'g_api_time' => time()
        ];

        // 如果API调用失败，更新状态为失败
        if (!isset($result['code']) || $result['code'] != 0) {
            $updateData['g_status'] = 'failed';
        }

        // 更新记录
        $this->model->where('id', $record['id'])->update($updateData);

        return $result;
    }
}
