# 函数作用域问题修复

## 问题分析

从截图可以看到表单字段没有正确填充，主要问题是：

### 1. 函数作用域问题
- `device_record.js` 中调用了 `validateDevice()` 函数
- 但 `validateDevice()` 函数定义在 `add.html` 中
- 导致函数调用失败，数据无法填充

### 2. 代码重复和冲突
- `add.html` 和 `device_record.js` 中都有类似的逻辑
- 可能导致事件绑定冲突

## 修复方案

### 1. 将 validateDevice 函数移到 device_record.js

```javascript
// 验证设备函数
function validateDevice(serialNumber, callback) {
    $.ajax({
        url: 'validate_device',
        type: 'GET',
        data: { serial_number: serialNumber },
        dataType: 'json',
        success: function(ret) {
            if (ret.code === 1) {
                window.deviceValidated = true;
                
                // 填充设备信息
                var data = ret.data;
                $('#c-g_device_id').val(data.g_id);
                setSelectpageValue('#c-g_game_id', data.g_game_id);
                
                // 注意：不覆盖二维码中的数据
                if (!$('#c-g_total_plays').val() || $('#c-g_total_plays').val() == '0') {
                    $('#c-g_total_plays').val(data.g_total_plays);
                }
                if (!$('#c-g_total_wins').val() || $('#c-g_total_wins').val() == '0') {
                    $('#c-g_total_wins').val(data.g_total_wins);
                }
                
                if (callback) callback(data);
            }
        }
    });
}
```

### 2. 添加事件绑定函数

```javascript
// 绑定设备验证相关事件
bindDeviceValidation: function() {
    // 验证设备按钮点击事件
    $(document).on('click', '#btn-validate-device', function() {
        var serialNumber = $('#c-g_serial_number').val().trim();
        if (!serialNumber) {
            Toastr.error('请输入设备序列号');
            return;
        }
        validateDevice(serialNumber);
    });

    // 设备序列号输入框失去焦点时自动验证
    $(document).on('blur', '#c-g_serial_number', function() {
        var serialNumber = $(this).val().trim();
        if (serialNumber) {
            validateDevice(serialNumber);
        }
    });

    // 表单提交前验证
    $(document).on('submit', '#add-form', function(e) {
        if (!window.deviceValidated) {
            e.preventDefault();
            Toastr.error('请先验证设备后再提交');
            return false;
        }
    });
}
```

### 3. 在页面初始化时调用

```javascript
add: function () {
    Controller.api.bindevent();
    Controller.api.initQrScanner();
    
    // 处理URL中的二维码数据
    processUrlQrData();
    
    // 绑定设备验证相关事件
    Controller.api.bindDeviceValidation();
}
```

## 数据填充逻辑

### 1. URL参数处理流程

```javascript
processUrlQrData() → 
解析qrData参数 → 
调用fillQrCodeData() → 
填充表单字段 → 
调用validateDevice()
```

### 2. 字段填充优先级

```javascript
// 二维码数据优先
$('#c-g_total_plays').val(qrData.playin);  // 528930
$('#c-g_total_wins').val(qrData.playout);  // 531090

// 设备验证时不覆盖已有数据
if (!$('#c-g_total_plays').val() || $('#c-g_total_plays').val() == '0') {
    $('#c-g_total_plays').val(data.g_total_plays);
}
```

## 预期修复效果

### 1. 表单字段正确填充
- 设备序列号：00900356
- 游戏ID：1005（下拉框正常显示）
- 总玩次数：528930
- 总赢次数：531090

### 2. 设备验证正常工作
- 自动验证设备
- 显示验证成功状态
- 填充设备ID

### 3. 表单提交验证
- 验证设备后才能提交
- 显示相应的错误提示

## 测试步骤

### 1. 重新访问URL
```
https://testdevice.91jdcd.com/FQgsXeSAhO.php/device/device_record/add?qrData=%7B%22dev%22%3A%2200900356%22%2C%22game%22%3A%221005%22%2C%22playin%22%3A%22528930%22%2C%22playout%22%3A%22531090%22%2C%22chk%22%3A12131%2C%22In%22%3A%220%22%2C%22Out%22%3A%220%22%2C%22enable%22%3A%22%22%7D&from_qr=1&dialog=1
```

### 2. 检查控制台
应该看到：
```
从URL解析的二维码数据： {dev: "00900356", game: "1005", playin: "528930", playout: "531090", ...}
填充二维码数据： {dev: "00900356", game: "1005", playin: "528930", playout: "531090", ...}
设置字段： #c-g_total_plays = 528930
设置字段： #c-g_total_wins = 531090
```

### 3. 检查表单
- 总玩次数字段显示：528930
- 总赢次数字段显示：531090
- 游戏ID下拉框有选项
- 设备验证状态显示成功

## 关键改进

### 1. 函数作用域统一
所有相关函数都在 `device_record.js` 中，避免作用域问题

### 2. 数据优先级明确
二维码数据优先，设备验证数据作为补充

### 3. 事件绑定集中
所有事件绑定都在 `device_record.js` 中管理

### 4. 错误处理完善
添加了完整的错误处理和用户提示

现在应该可以正确显示总玩次数和总赢次数了！
