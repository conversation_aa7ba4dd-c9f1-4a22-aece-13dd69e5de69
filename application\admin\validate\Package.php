<?php

namespace app\admin\validate;

use think\Validate;

class Package extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'board_id' => 'require|unique:package',
        'encrypt_id' => 'require|unique:package',
    ];
    
    /**
     * 提示消息
     */
    protected $message = [
        'board_id.require' => '主板ID不能为空',
        'board_id.unique' => '主板ID已存在',
        'encrypt_id.require' => '加密ID不能为空',
        'encrypt_id.unique' => '加密ID已存在',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['board_id', 'encrypt_id'],
        'edit' => ['board_id', 'encrypt_id'],
    ];
}
