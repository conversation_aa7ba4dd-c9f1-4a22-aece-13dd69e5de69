define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'device/device/index' + location.search,
                    add_url: 'device/device/add',
                    edit_url: 'device/device/edit',
                    del_url: 'device/device/del',
                    multi_url: 'device/device/multi',
                    import_url: 'device/device/import',
                    table: 'device',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'g_id',
                sortName: 'g_id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'g_id', title: __('G_id')},
                        {field: 'g_serial_number', title: __('G_serial_number'), operate: 'LIKE'},
                        {field: 'g_rid', title: __('G_rid'), operate: 'LIKE', visible: false},
                        {field: 'g_batch_number', title: __('G_batch_number'), operate: 'LIKE'}, 
                        {field: 'g_game_id', title: __('G_game_id')},
                        {field: 'g_total_plays', title: __('G_total_plays')},
                        {field: 'g_total_wins', title: __('G_total_wins')},
                        {field: 'g_status', title: __('G_status'), searchList: {"normal":__('Normal'),"locked":__('Locked')}, formatter: Table.api.formatter.status},
                        {field: 'g_createtime', title: __('G_createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_updatetime', title: __('G_updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_admin_id', title: __('G_admin_id')},
                        {field: 'g_merchant_id', title: __('G_merchant_id')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, buttons: [
                            {
                                name: 'coding',
                                text: __('打码'),
                                icon: 'fa fa-code',
                                classname: 'btn btn-xs btn-info btn-dialog',
                                url: 'device/device_record/add?g_device_id={g_id}&g_serial_number={g_serial_number}&g_rid={g_rid}&g_game_id={g_game_id}&g_total_plays={g_total_plays}&g_total_wins={g_total_wins}',
                                visible: function (row) {
                                    return true;
                                }
                            }
                        ], formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定通用打码按钮事件
            $(document).on('click', '.btn-coding', function () {
                // 显示扫码弹窗
                Layer.open({
                    type: 1,
                    title: '扫码打码',
                    area: ['90%', '90%'],
                    content: '<div id="qr-scanner-modal">' +
                             '<div id="qr-reader" style="width: 100%; height: 300px;"></div>' +
                             '<div style="text-align: center; margin-top: 10px;">' +
                             '<button type="button" class="btn btn-default" onclick="layer.closeAll()">取消</button>' +
                             '</div>' +
                             '</div>',
                    success: function(layero, index) {
                        // 初始化扫码器
                        initQrScanner(index);
                    }
                });
            });

            // 初始化扫码器
            function initQrScanner(layerIndex) {
                // 动态加载 html5-qrcode 库
                if (typeof Html5QrcodeScanner === 'undefined') {
                    var script = document.createElement('script');
                    script.src = '/assets/libs/html5-qrcode/html5-qrcode.min.js';
                    script.onload = function() {
                        startScanner(layerIndex);
                    };
                    document.head.appendChild(script);
                } else {
                    startScanner(layerIndex);
                }
            }

            // 启动扫码器
            function startScanner(layerIndex) {
                // 先尝试直接使用后置摄像头启动
                tryStartWithBackCamera(layerIndex);
            }

            // 尝试使用后置摄像头启动
            function tryStartWithBackCamera(layerIndex) {
                // 直接使用默认配置，让html5-qrcode插件自己处理权限
                console.log('使用默认配置启动扫码器');
                startWithDefaultConfig(layerIndex);
            }



            // 使用默认配置启动
            function startWithDefaultConfig(layerIndex) {
                var config = {
                    fps: 10,
                    qrbox: 250,
                    rememberLastUsedCamera: true,
                    showTorchButtonIfSupported: true,
                    // 优先使用后置摄像头
                    facingMode: "environment"
                };

                var html5QrcodeScanner = new Html5QrcodeScanner(
                    "qr-reader",
                    config,
                    /* verbose= */ false
                );

                html5QrcodeScanner.render(onScanSuccess, onScanFailure);

                // 自动处理权限和启动
                setTimeout(function() {
                    autoHandlePermissionsAndStart();
                }, 1000);

                function onScanSuccess(decodedText, decodedResult) {
                    console.log('扫码成功：', decodedText);

                    try {
                        // 解析二维码数据
                        var qrData = JSON.parse(decodedText);

                        // 停止扫码
                        html5QrcodeScanner.clear();

                        // 关闭扫码弹窗
                        layer.close(layerIndex);

                        // 根据设备序列号查询设备信息
                        if (qrData.dev) {
                            // 构建跳转URL
                            var qrDataParam = encodeURIComponent(JSON.stringify(qrData));
                            var url = 'device/device_record/add?qrData=' + qrDataParam + '&from_qr=1';

                            // 跳转到打码页面
                            Fast.api.open(url, '打码记录', {
                                area: ['90%', '90%'],
                                callback: function() {
                                    table.bootstrapTable('refresh');
                                }
                            });

                            Toastr.success('扫码成功，正在跳转...');
                        } else {
                            Toastr.error('二维码中缺少设备信息');
                        }

                    } catch (e) {
                        console.error('解析二维码数据失败：', e);
                        Toastr.error('二维码数据格式错误');
                    }
                }

                function onScanFailure(error) {
                    // 扫码失败，不需要处理
                }

                // 自动处理权限和启动
                function autoHandlePermissionsAndStart(preferredCameraId) {
                    var attempts = 0;
                    var maxAttempts = 5; // 增加尝试次数

                    function tryAutoStart() {
                        attempts++;
                        console.log('尝试自动启动，第', attempts, '次');

                        // 检查所有可能的按钮
                        var permissionButton = document.getElementById('html5-qrcode-button-camera-permission');
                        var startButton = document.getElementById('html5-qrcode-button-camera-start');
                        var stopButton = document.getElementById('html5-qrcode-button-camera-stop');

                        console.log('按钮状态检查：');
                        console.log('- 权限按钮：', permissionButton ? '存在' : '不存在',
                                   permissionButton && permissionButton.style.display !== 'none' ? '可见' : '隐藏');
                        console.log('- 开始按钮：', startButton ? '存在' : '不存在',
                                   startButton && startButton.style.display !== 'none' ? '可见' : '隐藏');
                        console.log('- 停止按钮：', stopButton ? '存在' : '不存在',
                                   stopButton && stopButton.style.display !== 'none' ? '可见' : '隐藏');

                        // 1. 首先检查权限按钮
                        if (permissionButton && permissionButton.style.display !== 'none') {
                            console.log('🔘 找到权限按钮，自动点击');
                            permissionButton.click();

                            // 点击权限按钮后等待一段时间再检查
                            setTimeout(function() {
                                tryAutoStart();
                            }, 2000); // 增加等待时间
                            return;
                        }

                        // 2. 检查开始按钮
                        if (startButton && startButton.style.display !== 'none') {
                            console.log('▶️ 找到开始按钮，准备选择摄像头');
                            selectFirstCameraAndStart();
                            return;
                        }

                        // 3. 检查是否已经在运行
                        if (stopButton && stopButton.style.display !== 'none') {
                            console.log('✅ 摄像头已经在运行');
                            return;
                        }

                        // 4. 继续等待
                        if (attempts < maxAttempts) {
                            setTimeout(tryAutoStart, 500);
                        } else {
                            console.log('⏰ 自动启动超时，请手动操作');
                            console.log('最终按钮状态：');
                            console.log('- 权限按钮：', permissionButton ? permissionButton.outerHTML : '不存在');
                            console.log('- 开始按钮：', startButton ? startButton.outerHTML : '不存在');
                            console.log('- 停止按钮：', stopButton ? stopButton.outerHTML : '不存在');
                        }
                    }

                    tryAutoStart();
                }

                // 选择序号最小的摄像头并启动
                function selectFirstCameraAndStart() {
                    // 等待摄像头选择下拉框加载
                    var attempts = 0;
                    var maxAttempts = 10;

                    function trySelectCamera() {
                        attempts++;

                        // 查找摄像头选择下拉框
                        var cameraSelect = document.getElementById('html5-qrcode-button-camera-select');
                        if (!cameraSelect) {
                            // 尝试其他可能的选择器
                            cameraSelect = document.querySelector('#qr-reader select');
                        }

                        if (cameraSelect && cameraSelect.options.length > 1) {
                            console.log('找到摄像头选择下拉框，选项数量：', cameraSelect.options.length);

                            // 打印所有摄像头选项
                            for (var i = 0; i < cameraSelect.options.length; i++) {
                                var option = cameraSelect.options[i];
                                console.log('摄像头选项', i + ':', option.text, option.value);
                            }

                            // 记录当前选择
                            var originalIndex = cameraSelect.selectedIndex;
                            var targetIndex = 1; // 选择序号最小的摄像头（通常索引1是第一个真实摄像头，索引0可能是"选择摄像头"）

                            // 如果只有一个摄像头选项，就选择它
                            if (cameraSelect.options.length === 2) {
                                targetIndex = 1;
                            } else if (cameraSelect.options.length > 2) {
                                // 多个摄像头时，选择第一个（序号最小的）
                                targetIndex = 1;
                            }

                            console.log('选择序号最小的摄像头，索引：', targetIndex, '名称：', cameraSelect.options[targetIndex].text);

                            // 只有在需要切换时才切换
                            if (targetIndex !== originalIndex) {
                                console.log('切换摄像头从', originalIndex, '到', targetIndex);
                                cameraSelect.selectedIndex = targetIndex;

                                // 触发选择变化事件
                                var changeEvent = new Event('change', { bubbles: true });
                                cameraSelect.dispatchEvent(changeEvent);

                                // 等待切换完成后启动
                                setTimeout(function() {
                                    startCameraWithRetry();
                                }, 1000);
                            } else {
                                console.log('已经选择了正确的摄像头，直接启动');
                                // 直接启动
                                setTimeout(function() {
                                    startCameraWithRetry();
                                }, 500);
                            }

                        } else if (attempts < maxAttempts) {
                            // 下拉框还没加载完成，继续等待
                            setTimeout(trySelectCamera, 500);
                        } else {
                            // 超时，直接启动默认摄像头
                            console.log('未找到摄像头选择下拉框，直接启动默认摄像头');
                            startCameraWithRetry();
                        }
                    }

                    // 带重试的启动摄像头
                    function startCameraWithRetry() {
                        var startAttempts = 0;
                        var maxStartAttempts = 3;

                        function tryStart() {
                            startAttempts++;
                            var startButton = document.getElementById('html5-qrcode-button-camera-start');

                            if (startButton && startButton.style.display !== 'none') {
                                startButton.click();
                                console.log('尝试启动摄像头，第', startAttempts, '次');

                                // 检查启动是否成功
                                setTimeout(function() {
                                    var video = document.querySelector('#qr-reader video');
                                    if (!video || video.videoWidth === 0) {
                                        if (startAttempts < maxStartAttempts) {
                                            console.log('摄像头启动失败，重试...');
                                            setTimeout(tryStart, 1000);
                                        } else {
                                            console.log('摄像头启动失败，已达到最大重试次数');
                                        }
                                    } else {
                                        console.log('摄像头启动成功');
                                    }
                                }, 2000);
                            } else if (startAttempts < maxStartAttempts) {
                                setTimeout(tryStart, 500);
                            }
                        }

                        tryStart();
                    }

                    trySelectCamera();
                }
            }
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
