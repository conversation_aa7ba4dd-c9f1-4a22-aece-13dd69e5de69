CREATE TABLE `g_device` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `g_serial_number` varchar(20) NOT NULL COMMENT '机器序列号',
  `g_game_id` int(11) NOT NULL COMMENT '游戏代号',
  `g_total_plays` int(11) DEFAULT '0' COMMENT '总玩次数',
  `g_total_wins` int(11) DEFAULT '0' COMMENT '总赢次数',
  `g_enable_modify_mode` tinyint(1) DEFAULT '0' COMMENT '允许修改模式参数',
  `g_enable_adjust_rate` tinyint(1) DEFAULT '0' COMMENT '允许调整游戏几率',
  `g_enable_clear_current_account` tinyint(1) DEFAULT '0' COMMENT '允许清除当期账目',
  `g_enable_clear_total_account` tinyint(1) DEFAULT '0' COMMENT '允许清除总的账目',
  `g_accept_modify_mode` tinyint(1) DEFAULT '0' COMMENT '接受修改模式参数',
  `g_accept_adjust_rate` tinyint(1) DEFAULT '0' COMMENT '接受调整游戏几率',
  `g_clear_current_account` tinyint(1) DEFAULT '0' COMMENT '清除当期账目',
  `g_clear_total_account` tinyint(1) DEFAULT '0' COMMENT '清除总的账目',
  `g_mode` tinyint(4) DEFAULT '0' COMMENT '算法模式控制参数(0-3)',
  `g_mode_config` varchar(255) DEFAULT '0' COMMENT '算法模式控制参数配置',
  `g_wave` tinyint(4) DEFAULT '0' COMMENT '算法波动参数(0-3)',
  `g_wave_config` varchar(255) DEFAULT '0' COMMENT '算法波动参数配置',
  `g_rate` tinyint(4) DEFAULT '0' COMMENT '游戏几率(0-7)',
  `g_rate_config` varchar(255) DEFAULT '0' COMMENT '游戏几率配置',
  `g_back` int(11) DEFAULT '0' COMMENT '备用参数',
  `g_back_config` varchar(255) DEFAULT '0' COMMENT '备用参数配置',
  `g_enable` int(11) DEFAULT '0' COMMENT '启用参数',
  `g_enable_config` varchar(255) DEFAULT '0' COMMENT '启用参数配置',
  `g_limit` int(11) DEFAULT '0' COMMENT '爆机数值(-32876-32875)',
  `g_limit_config` varchar(255) DEFAULT '0' COMMENT '爆机数值配置',
  `g_time` int(11) DEFAULT '0' COMMENT '游戏局数控制(0-32875)',
  `g_time_config` varchar(255) DEFAULT '0' COMMENT '游戏局数控制配置',
  `g_coin_score` tinyint(4) DEFAULT '0' COMMENT '1币得分数(0-3)',
  `g_cannon_value` int(11) DEFAULT '0' COMMENT '炮值',
  `g_fish_odds` int(11) DEFAULT '0' COMMENT '鱼赔率',
  `g_status` enum('normal','locked') DEFAULT 'normal' COMMENT '状态',
  `g_createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `g_updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `g_deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  `g_admin_id` int(11) NOT NULL COMMENT '关联管理员id',
  `g_merchant_id` int(11) NOT NULL COMMENT '关联商户id',
  PRIMARY KEY (`g_id`),
  UNIQUE KEY `g_serial_number` (`g_serial_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备表';




CREATE TABLE `g_device_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `g_device_id` int(11) NOT NULL COMMENT '关联设备id',
  `g_serial_number` varchar(20) NOT NULL COMMENT '机器序列号',
  `g_game_id` int(11) NOT NULL COMMENT '游戏代号',
  `g_total_plays` int(11) DEFAULT '0' COMMENT '总玩次数',
  `g_total_wins` int(11) DEFAULT '0' COMMENT '总赢次数',
  `g_enable_modify_mode` tinyint(1) DEFAULT '0' COMMENT '允许修改模式参数',
  `g_enable_adjust_rate` tinyint(1) DEFAULT '0' COMMENT '允许调整游戏几率',
  `g_enable_clear_current_account` tinyint(1) DEFAULT '0' COMMENT '允许清除当期账目',
  `g_enable_clear_total_account` tinyint(1) DEFAULT '0' COMMENT '允许清除总的账目',
  `g_accept_modify_mode` tinyint(1) DEFAULT '0' COMMENT '接受修改模式参数',
  `g_accept_adjust_rate` tinyint(1) DEFAULT '0' COMMENT '接受调整游戏几率',
  `g_clear_current_account` tinyint(1) DEFAULT '0' COMMENT '清除当期账目',
  `g_clear_total_account` tinyint(1) DEFAULT '0' COMMENT '清除总的账目',
  `g_mode` tinyint(4) DEFAULT '0' COMMENT '算法模式控制参数(0-3)',
  `g_wave` tinyint(4) DEFAULT '0' COMMENT '算法波动参数(0-3)',
  `g_rate` tinyint(4) DEFAULT '0' COMMENT '游戏几率(0-7)',
  `g_back` int(11) DEFAULT '0' COMMENT '备用参数',
  `g_limit` int(11) DEFAULT '0' COMMENT '爆机数值(-32876-32875)',
  `g_time` int(11) DEFAULT '0' COMMENT '游戏局数控制(0-32875)',
  `g_coin_score` tinyint(4) DEFAULT '0' COMMENT '1币得分数(0-3)',
  `g_cannon_value` int(11) DEFAULT '0' COMMENT '炮值',
  `g_fish_odds` int(11) DEFAULT '0' COMMENT '鱼赔率',
  `g_createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `g_updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `g_deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  `g_admin_id` int(11) NOT NULL COMMENT '关联管理员id',
  `g_merchant_id` int(11) NOT NULL COMMENT '关联商户id',
  `g_operator_id` int(11) NOT NULL COMMENT '操作用户id',
  
  `g_status` enum('pending','approved') DEFAULT 'pending' COMMENT '状态:pending=审核中,approved=已审核',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打码记录表';

CREATE TABLE `gc_game` (
  `game_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '游戏ID',
  `game_code` varchar(20) NOT NULL COMMENT '游戏代号',
  `game_name` varchar(100) NOT NULL COMMENT '游戏名称',
  `game_type` enum('Fishing', 'Coin_Pusher', 'Slots') NOT NULL COMMENT '游戏类型(Fishing=捕鱼, Coin_Pusher=推币, Slots=老虎机)',
  `created_at` int(11) DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`game_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏管理';
