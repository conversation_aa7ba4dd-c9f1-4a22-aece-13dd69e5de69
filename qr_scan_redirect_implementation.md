# 扫码跳转功能实现

## 功能概述

将扫码功能改为：先扫码获取参数，然后跳转到添加页面并传递参数，在页面打开时自动执行参数设置操作。

## 实现流程

### 1. 扫码流程改进

**原流程：** 扫码 → 直接填充当前页面表单

**新流程：** 扫码 → 构建URL参数 → 跳转到新页面 → 自动填充表单

### 2. 核心实现

#### 2.1 扫码成功处理 (device_record.js)

```javascript
function onScanSuccess(decodedText, decodedResult) {
    try {
        var qrData = JSON.parse(decodedText);
        
        // 停止扫码
        html5QrcodeScanner.clear();
        $("#scanModal").modal('hide');

        // 构建URL参数
        var urlParams = [];
        if (qrData.dev !== undefined) urlParams.push('g_serial_number=' + encodeURIComponent(qrData.dev));
        if (qrData.game !== undefined) urlParams.push('g_game_id=' + encodeURIComponent(qrData.game));
        if (qrData.playin !== undefined) urlParams.push('g_total_plays=' + encodeURIComponent(qrData.playin));
        if (qrData.playout !== undefined) urlParams.push('g_total_wins=' + encodeURIComponent(qrData.playout));
        if (qrData.chk !== undefined) urlParams.push('g_chk=' + encodeURIComponent(qrData.chk));
        
        // 添加扫码标识
        urlParams.push('from_qr=1');

        // 跳转到添加页面
        var url = 'device/device_record/add?' + urlParams.join('&');
        Fast.api.open(url, '打码记录', {
            area: ['90%', '90%'],
            callback: function() {
                // 刷新当前页面
                if (typeof Table !== 'undefined' && Table.api && Table.api.table) {
                    Table.api.table.bootstrapTable('refresh');
                }
            }
        });

        Toastr.success('扫码成功，正在跳转...');
    } catch (e) {
        console.error('扫码数据解析失败：', e);
        Toastr.error('扫码数据格式错误');
    }
}
```

#### 2.2 页面参数处理 (add.html)

```javascript
// 页面加载完成后检查URL参数
$(document).ready(function() {
    var urlParams = new URLSearchParams(window.location.search);
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1') {
        // 从二维码跳转过来，处理参数
        var qrData = {};
        
        // 提取所有相关参数
        if (urlParams.get('g_device_id')) qrData.g_device_id = urlParams.get('g_device_id');
        if (urlParams.get('g_serial_number')) qrData.g_serial_number = urlParams.get('g_serial_number');
        if (urlParams.get('g_rid')) qrData.g_rid = urlParams.get('g_rid');
        if (urlParams.get('g_game_id')) qrData.g_game_id = urlParams.get('g_game_id');
        if (urlParams.get('g_total_plays')) qrData.g_total_plays = urlParams.get('g_total_plays');
        if (urlParams.get('g_total_wins')) qrData.g_total_wins = urlParams.get('g_total_wins');
        if (urlParams.get('g_chk')) qrData.g_chk = urlParams.get('g_chk');
        
        // 延迟处理，确保页面完全加载
        setTimeout(function() {
            processQrParams(qrData);
        }, 500);
    }
});
```

#### 2.3 通用参数处理函数

```javascript
function processQrParams(qrData) {
    console.log('处理二维码参数：', qrData);
    
    // 如果有设备序列号，先验证设备
    if (qrData.g_serial_number) {
        validateDevice(qrData.g_serial_number, function(deviceData) {
            // 验证成功后填充其他参数
            fillFormWithQrData(qrData);
        });
    } else if (qrData.g_device_id) {
        // 如果有设备ID，通过设备ID获取设备信息
        getDeviceById(qrData.g_device_id, function(deviceData) {
            fillFormWithQrData(qrData);
        });
    } else {
        // 直接填充参数
        fillFormWithQrData(qrData);
    }
}
```

### 3. 设备验证增强

#### 3.1 支持设备ID和序列号验证

```php
public function validate_device()
{
    $serialNumber = $this->request->get('serial_number');
    $deviceId = $this->request->get('device_id');
    
    if (!$serialNumber && !$deviceId) {
        $this->error(__('Parameter %s can not be empty', 'serial_number or device_id'));
    }

    $deviceModel = new \app\admin\model\device\Device;
    
    // 根据不同参数查询设备
    if ($deviceId) {
        $device = $deviceModel->where('g_id', $deviceId)->find();
    } else {
        $device = $deviceModel->where('g_serial_number', $serialNumber)->find();
    }
    
    if (!$device) {
        $this->error('设备不存在，请先添加设备');
    }

    // 检查权限：group_id=7 的用户只能操作自己的设备
    if ($this->auth->group_id == 7 && $device['g_merchant_id'] != $this->auth->id) {
        $this->error('您没有权限操作此设备');
    }

    // 返回设备详细信息
    $this->success('设备验证成功', null, $deviceData);
}
```

## 功能特点

### 1. 数据一致性
- 通过设备验证确保数据准确性
- 自动获取最新的设备信息
- 避免二维码数据过期问题

### 2. 权限控制
- group_id=7 用户只能操作自己的设备
- 设备不存在时提示用户先添加设备
- 无权限时阻止操作

### 3. 用户体验
- 扫码后自动跳转到新页面
- 参数自动填充，无需手动输入
- 清晰的成功和错误提示

### 4. 灵活性
- 支持设备ID和序列号两种方式
- 兼容现有的手动输入功能
- 保持原有的验证机制

## URL参数格式

```
device/device_record/add?g_device_id={g_id}&g_serial_number={g_serial_number}&g_rid={g_rid}&g_game_id={g_game_id}&g_total_plays={g_total_plays}&g_total_wins={g_total_wins}&g_chk={g_chk}&from_qr=1
```

### 参数说明
- `g_device_id`: 设备ID
- `g_serial_number`: 设备序列号
- `g_rid`: 设备RID
- `g_game_id`: 游戏ID
- `g_total_plays`: 总玩次数
- `g_total_wins`: 总赢次数
- `g_chk`: 校验码
- `from_qr=1`: 扫码标识

## 错误处理

### 1. 设备不存在
- 提示："设备不存在，请先添加设备"
- 阻止表单提交

### 2. 权限不足
- 提示："您没有权限操作此设备"
- 阻止继续操作

### 3. 参数错误
- 提示："扫码数据格式错误"
- 记录错误日志

## 测试场景

1. **正常扫码流程**
   - 扫描有效二维码
   - 自动跳转到新页面
   - 参数正确填充

2. **设备验证**
   - 设备存在且有权限：正常填充
   - 设备不存在：显示错误提示
   - 无权限：显示权限错误

3. **参数处理**
   - 有设备序列号：先验证再填充
   - 有设备ID：通过ID获取信息
   - 无设备信息：直接填充其他参数

## 修改的文件

1. `public/assets/js/backend/device/device_record.js` - 扫码跳转逻辑
2. `application/admin/view/device/device_record/add.html` - 参数处理逻辑
3. `application/admin/controller/device/DeviceRecord.php` - 设备验证增强
