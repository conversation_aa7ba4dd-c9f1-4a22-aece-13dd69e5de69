define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/index' + location.search,
                    add_url: 'game/add',
                    edit_url: 'game/edit',
                    del_url: 'game/del',
                    multi_url: 'game/multi',
                    import_url: 'game/import',
                    table: 'game',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'game_id',
                sortName: 'game_id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'game_id', title: __('Game_id')},
                        {field: 'game_code', title: __('Game_code'), operate: 'LIKE'},
                        {field: 'game_name', title: __('Game_name'), operate: 'LIKE'},
                        {field: 'game_type', title: __('Game_type'), searchList: {"Fishing":__('Game_type Fishing'),"Coin_Pusher":__('Game_type Coin_Pusher'),"Slots":__('Game_type Slots')}, formatter: Table.api.formatter.normal},
                        {field: 'created_at', title: __('Created_at')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
