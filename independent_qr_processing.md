# 独立扫码处理功能实现

## 功能概述

将扫码成功处理逻辑独立出来，创建一个通用的 `processQrCodeData` 函数，可以在扫码时和页面加载时重复使用。

## 核心实现

### 1. 独立的处理函数

```javascript
// 全局函数，可在任何地方调用
window.processQrCodeData = function(qrData, isFromUrl) {
    console.log('处理二维码数据：', qrData, '来源：', isFromUrl ? 'URL参数' : '扫码');
    
    if (isFromUrl) {
        // 来自URL参数的处理逻辑
        handleUrlParams(qrData);
    } else {
        // 来自扫码的处理逻辑
        handleScanData(qrData);
    }
};
```

### 2. 两种处理模式

#### 模式1：扫码处理 (isFromUrl = false)
- 关闭当前窗口
- 传递完整的二维码参数到新页面
- 跳转到新的打码页面

#### 模式2：URL参数处理 (isFromUrl = true)
- 根据 `g_serial_number` 自动获取设备信息
- 补充缺失的 `g_id` 参数
- 直接填充表单数据

## 详细流程

### 扫码成功流程

```javascript
function onScanSuccess(decodedText, decodedResult) {
    try {
        var qrData = JSON.parse(decodedText);
        
        // 停止扫码
        html5QrcodeScanner.clear();
        $("#scanModal").modal('hide');

        // 调用独立的处理函数
        window.processQrCodeData(qrData, false);
    } catch (e) {
        console.error('扫码数据解析失败：', e);
        Toastr.error('扫码数据格式错误');
    }
}
```

### URL参数处理流程

```javascript
$(document).ready(function() {
    var urlParams = new URLSearchParams(window.location.search);
    var fromQr = urlParams.get('from_qr');
    
    if (fromQr === '1') {
        // 提取所有URL参数
        var qrData = {};
        for (var pair of urlParams.entries()) {
            var key = pair[0];
            var value = pair[1];
            if (key !== 'from_qr') {
                qrData[key] = value;
            }
        }
        
        // 延迟调用处理函数
        setTimeout(function() {
            window.processQrCodeData(qrData, true);
        }, 500);
    }
});
```

### 设备信息补充逻辑

```javascript
if (isFromUrl && (qrData.dev || qrData.g_serial_number)) {
    var serialNumber = qrData.dev || qrData.g_serial_number;
    
    // 根据序列号获取设备信息
    $.ajax({
        url: 'device/device_record/validate_device',
        type: 'GET',
        data: { serial_number: serialNumber },
        success: function(ret) {
            if (ret.code === 1) {
                // 补充g_id到二维码数据中
                qrData.g_id = ret.data.g_id;
                qrData.device_id = ret.data.g_id;
                
                // 填充所有表单数据
                fillFormFromQrData(qrData);
            }
        }
    });
}
```

## 参数传递机制

### 完整参数传递

扫码时传递所有二维码参数：

```javascript
// 构建URL参数，传递完整的二维码数据
var urlParams = [];
for (var key in qrData) {
    if (qrData[key] !== undefined && qrData[key] !== null) {
        urlParams.push(encodeURIComponent(key) + '=' + encodeURIComponent(qrData[key]));
    }
}
urlParams.push('from_qr=1');

var url = 'device/device_record/add?' + urlParams.join('&');
```

### URL示例

```
device/device_record/add?dev=DEV001&game=GAME001&playin=100&playout=50&chk=ABC123&rid=RID001&from_qr=1
```

## 字段映射

### 二维码字段 → 表单字段

```javascript
var fieldMapping = {
    'device_id': 'g_device_id',
    'g_id': 'g_device_id',
    'dev': 'g_serial_number',
    'rid': 'g_rid', 
    'game': 'g_game_id',
    'playin': 'g_total_plays',
    'playout': 'g_total_wins',
    'chk': 'g_chk'
};
```

## 关键特性

### 1. 自动补充 g_id
- 二维码中通常没有 `g_id` 参数
- 根据 `g_serial_number` 自动查询数据库
- 获取对应的 `g_id` 并补充到数据中

### 2. 完整参数保留
- 传递二维码中的所有参数
- 不丢失任何原始数据
- 支持扩展新的参数

### 3. 双重调用支持
- 扫码时调用：跳转到新页面
- 页面加载时调用：处理URL参数

### 4. 错误处理
- 设备不存在时的错误提示
- 权限不足时的错误处理
- 网络请求失败的重试机制

## 使用场景

### 场景1：扫码跳转
1. 用户在列表页面扫码
2. 解析二维码数据
3. 关闭当前窗口
4. 跳转到新的打码页面
5. 传递完整参数

### 场景2：页面加载
1. 新页面加载完成
2. 检测到 `from_qr=1` 标识
3. 提取所有URL参数
4. 调用处理函数
5. 自动填充表单

## 调试信息

### 控制台输出
```
处理二维码数据： {dev: "DEV001", game: "GAME001", ...} 来源： URL参数
填充表单数据： {dev: "DEV001", g_id: "123", ...}
设置字段： #c-g_device_id = 123
设置字段： #c-g_serial_number = DEV001
```

### 验证步骤
1. 检查 `window.processQrCodeData` 函数是否存在
2. 确认URL参数是否正确传递
3. 验证设备信息是否成功获取
4. 检查表单字段是否正确填充

## 优势

1. **代码复用**：一个函数处理两种场景
2. **参数完整**：不丢失任何二维码数据
3. **自动补充**：智能获取缺失的设备信息
4. **错误处理**：完善的异常处理机制
5. **调试友好**：详细的日志输出

## 文件修改

1. `public/assets/js/backend/device/device_record.js`
   - 添加 `window.processQrCodeData` 全局函数
   - 简化 `onScanSuccess` 函数
   - 添加 `fillFormFromQrData` 函数

2. `application/admin/view/device/device_record/add.html`
   - 修改URL参数处理逻辑
   - 调用全局处理函数
   - 移除重复的处理代码
