define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'layer'], function ($, undefined, Backend, Table, Form, Layer) {

    // 使用selectpage原生API设置值
    function setSelectpageValue(selector, value, maxRetries = 10, delay = 300) {
        var retryCount = 0;

        function trySetValue() {
            var $element = $(selector);
            if ($element.length === 0) {
                return;
            }

            // 获取selectpage对象
            var selectPageObject = $element.data("selectPageObject");

            if (selectPageObject && selectPageObject.elem && selectPageObject.elem.hidden) {
                // 使用原生API设置值
                selectPageObject.elem.hidden.val(value);
                $element.selectPageRefresh();
                console.log('Selectpage value set successfully:', selector, value);
                return;
            }

            // 如果还没初始化，继续重试
            retryCount++;
            if (retryCount < maxRetries) {
                setTimeout(trySetValue, delay);
            } else {
                // 最后一次尝试，使用传统方法
                console.log('Selectpage not initialized, using fallback method:', selector, value);
                $element.val(value).trigger('change');
            }
        }

        // 立即尝试一次
        trySetValue();
    }

    // 统一的二维码数据填充函数
    window.fillQrCodeData = function(qrData) {
        console.log('填充二维码数据：', qrData);

        // 直接设置关键字段
        if (qrData.playin) {
            $('#c-g_total_plays').val(qrData.playin);
            console.log('设置总玩次数：', qrData.playin);
        }
        if (qrData.playout) {
            $('#c-g_total_wins').val(qrData.playout);
            console.log('设置总赢次数：', qrData.playout);
        }
        if (qrData.chk) {
            $('#c-g_chk').val(qrData.chk);
            console.log('设置校验码：', qrData.chk);
        }
        if (qrData.In) {
            $('#c-g_in').val(qrData.In);
            console.log('设置In：', qrData.In);
        }
        if (qrData.Out) {
            $('#c-g_out').val(qrData.Out);
            console.log('设置Out：', qrData.Out);
        }

        // 处理enable参数
        if (qrData.enable) {
            console.log('处理enable参数：', qrData.enable);

            // 先清空选择
            $('#c-g_enable option[value=""]').prop('selected', true);

            if (qrData.enable && qrData.enable.includes('t')) {
                $('#c-g_enable option[value="t"]').prop('selected', true);
            }
            if(qrData.enable && qrData.enable.includes('c')){
                $('#c-g_enable option[value="c"]').prop('selected', true);
            }
            if(qrData.enable && qrData.enable.includes('ct')){
                $('#c-g_enable option[value="ct"]').prop('selected', true);
            }
            $("#c-g_enable").trigger('change');

            if(qrData.enable && qrData.enable.includes('r')){
                $('#c-g_cannon_value').prop('checked', true);
            }
            if(qrData.enable && qrData.enable.includes('m')){
                $('#c-g_fish_odds').prop('checked', true);
            }
        }

        // 设置设备验证状态
        window.deviceValidated = true;
        if ($('#device-status').length > 0) {
            $('#device-status').html('<span class="text-success">✓ 设备验证成功（来自二维码）</span>');
        }

        Toastr.success('二维码数据已填充');
    };

    // 验证设备函数
    function validateDevice(serialNumber, callback) {
        $.ajax({
            url: 'validate_device',
            type: 'GET',
            data: { serial_number: serialNumber },
            dataType: 'json',
            beforeSend: function() {
                if ($('#btn-validate-device').length > 0) {
                    $('#btn-validate-device').prop('disabled', true).text('验证中...');
                }
                if ($('#device-status').length > 0) {
                    $('#device-status').html('<span class="text-info">正在验证设备...</span>');
                }
            },
            success: function(ret) {
                if (ret.code === 1) {
                    window.deviceValidated = true;
                    if ($('#device-status').length > 0) {
                        $('#device-status').html('<span class="text-success">✓ 设备验证成功</span>');
                    }

                    // 填充设备信息到表单
                    var data = ret.data;
                    $('#c-g_device_id').val(data.g_id);

                    // 使用延迟设置selectpage值
                    setSelectpageValue('#c-g_game_id', data.g_game_id);

                    // 注意：不覆盖二维码中的总玩次数和总赢次数
                    if (!$('#c-g_total_plays').val() || $('#c-g_total_plays').val() == '0') {
                        $('#c-g_total_plays').val(data.g_total_plays);
                    }
                    if (!$('#c-g_total_wins').val() || $('#c-g_total_wins').val() == '0') {
                        $('#c-g_total_wins').val(data.g_total_wins);
                    }

                    // 执行回调
                    if (callback) callback(data);

                } else {
                    window.deviceValidated = false;
                    if ($('#device-status').length > 0) {
                        $('#device-status').html('<span class="text-danger">✗ ' + ret.msg + '</span>');
                    }
                    Toastr.error(ret.msg);
                }
            },
            error: function() {
                window.deviceValidated = false;
                if ($('#device-status').length > 0) {
                    $('#device-status').html('<span class="text-danger">✗ 验证失败，请重试</span>');
                }
                Toastr.error('设备验证失败，请重试');
            },
            complete: function() {
                if ($('#btn-validate-device').length > 0) {
                    $('#btn-validate-device').prop('disabled', false).text('验证设备');
                }
            }
        });
    }

    // URL参数处理函数 - 简化版本
    function processUrlQrData() {
        // 检查URL参数中是否有二维码数据
        var urlParams = new URLSearchParams(window.location.search);
        var qrDataParam = urlParams.get('qrData');
        var fromQr = urlParams.get('from_qr');

        if (fromQr === '1' && qrDataParam) {
            try {
                // 解析二维码数据
                var qrData = JSON.parse(decodeURIComponent(qrDataParam));
                console.log('从URL解析的二维码数据：', qrData);

                // 延迟处理，确保页面完全加载
                setTimeout(function() {
                    // 调用统一的填充函数
                    window.fillQrCodeData(qrData);
                }, 500);

            } catch (e) {
                console.error('解析URL中的二维码数据失败：', e);
                Toastr.error('二维码数据格式错误');
            }
        }
    }

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'device/device_record/index' + location.search,
                    add_url: 'device/device_record/add',
                    edit_url: 'device/device_record/edit',
                    del_url: 'device/device_record/del',
                    multi_url: 'device/device_record/multi',
                    import_url: 'device/device_record/import',
                    table: 'device_record',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'g_serial_number', title: __('G_serial_number'), operate: 'LIKE'},
                        {field: 'g_game_id', title: __('G_game_id')},
                        {field: 'g_total_plays', title: __('G_total_plays')},
                        {field: 'g_total_wins', title: __('G_total_wins')},
                        {field: 'g_status', title: __('G_status'), searchList: {"pending":__('G_status pending'),"approved":__('G_status approved')}, formatter: Table.api.formatter.status},
                        {field: 'g_createtime', title: __('G_createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_updatetime', title: __('G_updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'g_admin_id', title: __('G_admin_id')},
                        {field: 'g_merchant_id', title: __('G_merchant_id')},
                        {field: 'g_operator_id', title: __('G_operator_id')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            Controller.api.initQrScanner();

            // 处理URL中的二维码数据
            processUrlQrData();
        },
        edit: function () {
            Controller.api.bindevent();
            
        },
        api: { 
            bindevent: function () {

                Form.api.bindevent($("form[role=form]"),  function(data,ret)  {

                        
                        // 构建结果HTML
                        var resultHtml = '<div style="text-align:center;padding:20px;">' +
                            '<h2 style="color:green;font-size:28px;margin-bottom:30px;"><i class="fa fa-check-circle"></i> 打码成功</h2>' +
                            '<div style="margin:30px auto;font-size:20px;max-width:600px;text-align:left;">';
                            // '<p><strong>设备序列号:</strong> ' + ret.data.g_serial_number + '</p>' +
                            // '<p><strong>游戏代号:</strong> ' + ret.data.g_game_id + '</p>' +
                            // '<p><strong>总玩次数:</strong> ' + ret.data.g_total_plays + '</p>' +
                            // '<p><strong>总赢次数:</strong> ' + ret.data.g_total_wins + '</p>';
                            
                        // 添加API响应信息（如果有）
                        if (ret.msg) { 
                            resultHtml += '<p><strong>结果:</strong> ' + ret.msg + '</p>';
                        }

                        
                        resultHtml += '</div>' +
                            '<div style="margin-top:50px;">' +
                            '<button type="button" class="btn btn-primary btn-lg" onclick="Layer.closeAll();">关闭</button>' +
                            '</div>' +
                            '</div>';
                        // 使用layer弹出全屏窗口
                        Layer.open({
                            type: 1,
                            title: false,
                            closeBtn: 1,
                            area: ['100%', '100%'],
                            shade: 0.8,
                            id: 'LAY_success',
                            btn: false,
                            content: resultHtml,
                            yes: function(){
                                Layer.closeAll();
                            }
                        });
                                                
                        return false;

                },null, null);
                
            },
            initQrScanner: function() {
                // 检查是否在添加页面
                if ($("form[role=form]").length <= 0) {
                    return;
                }

                // 如果扫码按钮已存在，不重复创建
                if ($("#btn-scan").length > 0) {
                    return;
                }

                // 在表单顶部添加扫码按钮
                if ($("#scan-button-container").length <= 0) {
                    var scanButtonHtml = '<div class="form-group" id="scan-button-container">'+
                        '<label class="control-label col-xs-12 col-sm-2">扫码填写:</label>'+
                        '<div class="col-xs-12 col-sm-8">'+
                        '<button type="button" class="btn btn-info btn-scan" id="btn-scan"><i class="fa fa-qrcode"></i> 扫描二维码</button>'+
                        '</div>'+
                        '</div>';
                    $("form[role=form]").prepend(scanButtonHtml);
                }

                // 如果模态框不存在，添加模态框
                if ($("#scanModal").length <= 0) {
                    var modalHtml = '<div class="modal fade" id="scanModal" tabindex="-1" role="dialog" aria-labelledby="scanModalLabel">'+
                        '<div class="modal-dialog" role="document">'+
                        '<div class="modal-content">'+
                        '<div class="modal-header">'+
                        '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'+
                        '<h4 class="modal-title" id="scanModalLabel">扫描二维码</h4>'+
                        '</div>'+
                        '<div class="modal-body">'+
                        '<div id="reader" style="width:100%;"></div>'+
                        '<div class="text-center" style="margin-top:10px;">'+
                        '<p class="text-muted">请将二维码对准摄像头，系统会自动识别</p>'+
                        '</div>'+
                        '</div>'+
                        '<div class="modal-footer">'+
                        '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>'+
                        '</div>'+
                        '</div>'+
                        '</div>'+
                        '</div>';
                    $("body").append(modalHtml);
                }

                // 扫码器实例
                var html5QrcodeScanner = null;

                // 加载扫码库
                function loadQrCodeLibrary(callback) {
                    // 如果已经加载，直接调用回调
                    if (typeof window.Html5QrcodeScanner !== 'undefined') {
                        console.log('Html5QrcodeScanner 库已加载');
                        callback();
                        return;
                    }

                    console.log('开始加载 Html5QrcodeScanner 库');
                    var script = document.createElement('script');
                    script.src = "/assets/libs/html5-qrcode/html5-qrcode.min.js";
                    script.async = true;

                    script.onload = function() {
                        console.log('Html5QrcodeScanner 库加载成功');
                        // 确保库已经加载完成
                        setTimeout(function() {
                            if (typeof window.Html5QrcodeScanner !== 'undefined') {
                                callback();
                            } else {
                                console.error('Html5QrcodeScanner 库加载完成但未定义');
                                Layer.msg('扫码库加载失败，请刷新页面后重试', {icon: 2});
                            }
                        }, 500); // 等待500毫秒确保库已加载完成
                    };

                    script.onerror = function() {
                        console.error('Html5QrcodeScanner 库加载失败');
                        Layer.msg('扫码库加载失败，请检查网络连接后重试', {icon: 2});
                    };

                    document.head.appendChild(script);
                }

                // 点击扫码按钮
                $("#btn-scan").on("click", function() {
                    // 检查浏览器是否支持摄像头访问
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        Layer.msg('您的浏览器不支持摄像头访问，请https和使用现代浏览器', {icon: 2});
                        return;
                    }

                    // 加载扫码库并打开模态框
                    loadQrCodeLibrary(function() {
                        $("#scanModal").modal('show');
                    });
                });

                // 模态框显示时初始化扫码器
                $('#scanModal').on('shown.bs.modal', function() {
                    // 如果扫码器实例已存在，先清除
                    if (html5QrcodeScanner) {
                        html5QrcodeScanner.clear();
                        html5QrcodeScanner = null;
                    }

                    // 初始化扫码器
                    try {
                        // 处理扫描成功的回调函数
                        function onScanSuccess(decodedText, decodedResult) {
                            console.log(`扫描成功: ${decodedText}`, decodedResult);

                            try {
                                // 解析JSON数据
                                var qrData = JSON.parse(decodedText);

                                // 停止扫码
                                html5QrcodeScanner.clear();
                                $("#scanModal").modal('hide');

                                // 调用统一的填充函数
                                window.fillQrCodeData(qrData);

                                

                                // 关闭模态框
                                setTimeout(function() {
                                    $("#scanModal").modal('hide');
                                }, 1000);

                                // 停止扫码器
                                if (html5QrcodeScanner) {
                                    html5QrcodeScanner.clear();
                                }
                            } catch (e) {
                                console.error('解析JSON数据失败:', e);
                                Layer.msg('解析失败! 请确保二维码包含有效的JSON数据', {icon: 2});
                            }
                        }

                        // 处理扫描错误的回调函数
                        function onScanError(errorMessage) {
                            // 错误处理，一般不需要显示给用户
                            console.log('扫描错误:', errorMessage);
                        }

                        // 创建扫码器实例
                        html5QrcodeScanner = new Html5QrcodeScanner(
                            "reader",
                            {
                                fps: 1,
                                qrbox: 250,
                                rememberLastUsedCamera: true,
                                // 默认使用后置摄像头
                                facingMode: 'environment',
                                // 支持双击切换摄像头
                                showTorchButtonIfSupported: true,
                                showZoomSliderIfSupported: true
                            },
                            /* verbose= */ false);

                        // 渲染扫码器
                        html5QrcodeScanner.render(onScanSuccess, onScanError);

                        // 添加双击切换摄像头的功能
                        setTimeout(function() {
                            var readerElement = document.getElementById('reader');
                            if (readerElement) {
                                // 记录双击时间
                                var lastTapTime = 0;

                                readerElement.addEventListener('click', function(e) {
                                    var currentTime = new Date().getTime();
                                    var tapLength = currentTime - lastTapTime;

                                    // 如果是双击（小于500毫秒）
                                    if (tapLength < 500 && tapLength > 0) {
                                        console.log('双击检测到，尝试切换摄像头');

                                        // 尝试切换摄像头
                                        try {
                                            // 获取切换摄像头的按钮
                                            var cameraSelectionContainer = readerElement.querySelector('#html5-qrcode-select-camera');
                                            if (cameraSelectionContainer) {
                                                var cameraSelection = cameraSelectionContainer.querySelector('select');
                                                if (cameraSelection && cameraSelection.options.length > 1) {
                                                    // 选择下一个摄像头
                                                    var currentIndex = cameraSelection.selectedIndex;
                                                    var nextIndex = (currentIndex + 1) % cameraSelection.options.length;
                                                    cameraSelection.selectedIndex = nextIndex;

                                                    // 触发change事件
                                                    var event = new Event('change');
                                                    cameraSelection.dispatchEvent(event);

                                                    Layer.msg('切换到摄像头: ' + cameraSelection.options[nextIndex].text, {icon: 1});
                                                }
                                            }
                                        } catch (err) {
                                            console.error('切换摄像头时出错:', err);
                                        }
                                    }

                                    lastTapTime = currentTime;
                                });
                            }
                        }, 1000); // 等待扫码器渲染完成

                    } catch (error) {
                        console.error('初始化扫码器时出错:', error);
                        Layer.msg('初始化扫码器时出错，请刷新页面后重试', {icon: 2});
                    }
                });

                // 模态框关闭时停止扫码
                $('#scanModal').on('hidden.bs.modal', function() {
                    // 停止扫码器
                    if (html5QrcodeScanner) {
                        try {
                            html5QrcodeScanner.clear();
                            html5QrcodeScanner = null;
                        } catch (error) {
                            console.error('停止扫码器时出错:', error);
                        }
                    }
                });
            },

            // 绑定设备验证相关事件
            bindDeviceValidation: function() {
                // 验证设备按钮点击事件
                $(document).on('click', '#btn-validate-device', function() {
                    var serialNumber = $('#c-g_serial_number').val().trim();
                    if (!serialNumber) {
                        Toastr.error('请输入设备序列号');
                        return;
                    }
                    validateDevice(serialNumber);
                });

                // 设备序列号输入框失去焦点时自动验证
                $(document).on('blur', '#c-g_serial_number', function() {
                    var serialNumber = $(this).val().trim();
                    if (serialNumber) {
                        validateDevice(serialNumber);
                    }
                });

                // 表单提交前验证
                $(document).on('submit', '#add-form', function(e) {
                    if (!window.deviceValidated) {
                        e.preventDefault();
                        Toastr.error('请先验证设备后再提交');
                        return false;
                    }
                });
            }
        }
    };
    return Controller;
});
