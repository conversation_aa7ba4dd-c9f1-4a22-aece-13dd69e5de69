<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
<script>
    // 游戏配置数据
    var gameList = {:json_encode($gameList)};
    var currentGameId = "{$currentGameId|default=''}";

    // 监听游戏选择变化
    $(document).on('change', '#c-g_game_id', function() {
        var gameId = $(this).val();
        if (!gameId || !gameList[gameId]) {
            return;
        }

        // 重新加载页面，带上游戏ID参数
        var url = location.href.split('?')[0] + '?g_game_id=' + gameId;
        if ($('#c-g_device_id').val()) {
            url += '&g_device_id=' + $('#c-g_device_id').val();
        }
        if ($('#c-g_serial_number').val()) {
            url += '&g_serial_number=' + $('#c-g_serial_number').val();
        }
        if ($('#c-g_total_plays').val()) {
            url += '&g_total_plays=' + $('#c-g_total_plays').val();
        }
        if ($('#c-g_total_wins').val()) {
            url += '&g_total_wins=' + $('#c-g_total_wins').val();
        }
        location.href = url;
    });

    // 解析二维码数据
    function parseQRCodeData(data) {
        try {
            var jsonData = JSON.parse(data);
            console.log('QR Code data:', jsonData);

            // 映射字段
            var fieldMapping = {
                'device_id': 'g_device_id',
                'dev': 'g_serial_number',
                'rid': 'g_rid',
                'game': 'g_game_id',
                'playin': 'g_total_plays',
                'playout': 'g_total_wins',
                'chk': 'g_chk'
            };

            // 填充表单
            for (var key in jsonData) {
                if (fieldMapping[key]) {
                    var fieldId = '#c-' + fieldMapping[key];
                    $(fieldId).val(jsonData[key]);

                    // 如果是游戏ID，使用延迟设置
                    if (fieldMapping[key] === 'g_game_id') {
                        setSelectpageValue(fieldId, jsonData[key]);
                    }

                    // 如果是设备序列号，自动验证设备
                    if (fieldMapping[key] === 'g_serial_number' && jsonData[key]) {
                        validateDevice(jsonData[key]);
                    }
                }
            }

            $('#c-g_enable option[value=""]').prop('selected', true);

            if (jsonData.enable && jsonData.enable.includes('t')) {
                $('#c-g_enable option[value="t"]').prop('selected', true);
            }
            if(jsonData.enable && jsonData.enable.includes('c')){
                $('#c-g_enable option[value="c"]').prop('selected', true);
            }
            if(jsonData.enable && jsonData.enable.includes('ct')){
                $('#c-g_enable option[value="ct"]').prop('selected', true);
            }
            if(jsonData.enable && jsonData.enable.includes('r')){
                $('#c-g_cannon_value').prop('checked', true);
            }
            if(jsonData.enable && jsonData.enable.includes('m')){
                $('#c-g_cannon_value').prop('checked', true);
            }


        } catch (e) {
            Toastr.error('二维码数据格式错误，请确保是JSON格式');
            console.error('Error parsing QR code data:', e);
        }
    }

    // 初始化扫描器
    var html5QrcodeScanner = null;

    // 打开扫描器
    function openScanner() {
        if (html5QrcodeScanner) {
            html5QrcodeScanner.clear();
        }

        // 创建扫描器容器
        if (!$('#qr-reader').length) {
            $('body').append('<div id="qr-reader-modal" class="modal fade" tabindex="-1" role="dialog">'+
                '<div class="modal-dialog" role="document">'+
                    '<div class="modal-content">'+
                        '<div class="modal-header">'+
                            '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'+
                            '<h4 class="modal-title">扫描二维码</h4>'+
                        '</div>'+
                        '<div class="modal-body">'+
                            '<div id="qr-reader" style="width:100%"></div>'+
                        '</div>'+
                        '<div class="modal-footer">'+
                            '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>'+
                            '<button type="button" class="btn btn-primary" id="switch-camera">切换摄像头</button>'+
                        '</div>'+
                    '</div>'+
                '</div>'+
            '</div>');
        }

        // 显示模态框
        $('#qr-reader-modal').modal('show');

        // 初始化扫描器
        var currentCamera = 'environment'; // 默认后置摄像头

        function startScanner(cameraId) {
            html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader",
                {
                    fps: 10,
                    qrbox: 250,
                    rememberLastUsedCamera: true,
                    aspectRatio: 1.0,
                    formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE],
                    showTorchButtonIfSupported: true,
                    defaultZoomValueIfSupported: 2
                }
            );

            html5QrcodeScanner.render(function(decodedText, decodedResult) {
                // 扫描成功后的回调
                console.log(`QR Code detected: ${decodedText}`, decodedResult);
                $('#qr-reader-modal').modal('hide');
                parseQRCodeData(decodedText);
                html5QrcodeScanner.clear();
            });
        }

        // 开始扫描
        startScanner();

        // 切换摄像头
        $('#switch-camera').on('click', function() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
                currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
                startScanner(currentCamera);
            }
        });

        // 模态框关闭时清理扫描器
        $('#qr-reader-modal').on('hidden.bs.modal', function() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
            }
        });
    }

    // 使用selectpage原生API设置值
    function setSelectpageValue(selector, value, maxRetries = 10, delay = 300) {
        var retryCount = 0;

        function trySetValue() {
            var $element = $(selector);
            if ($element.length === 0) {
                return;
            }

            // 获取selectpage对象
            var selectPageObject = $element.data("selectPageObject");

            if (selectPageObject && selectPageObject.elem && selectPageObject.elem.hidden) {
                // 使用原生API设置值
                selectPageObject.elem.hidden.val(value);
                $element.selectPageRefresh();
                console.log('Selectpage value set successfully:', selector, value);
                return;
            }

            // 如果还没初始化，继续重试
            retryCount++;
            if (retryCount < maxRetries) {
                setTimeout(trySetValue, delay);
            } else {
                // 最后一次尝试，使用传统方法
                console.log('Selectpage not initialized, using fallback method:', selector, value);
                $element.val(value).trigger('change');
            }
        }

        // 立即尝试一次
        trySetValue();
    }

    // 注意：processQrParams 和相关函数已移动到 device_record.js 中作为全局函数

    // 设备验证功能
    var deviceValidated = false;

    // 验证设备按钮点击事件
    $(document).on('click', '#btn-validate-device', function() {
        var serialNumber = $('#c-g_serial_number').val().trim();
        if (!serialNumber) {
            Toastr.error('请输入设备序列号');
            return;
        }

        validateDevice(serialNumber);
    });

    // 设备序列号输入框失去焦点时自动验证
    $(document).on('blur', '#c-g_serial_number', function() {
        var serialNumber = $(this).val().trim();
        if (serialNumber) {
            validateDevice(serialNumber);
        }
    });

    // 验证设备函数
    function validateDevice(serialNumber, callback) {
        $.ajax({
            url: 'validate_device',
            type: 'GET',
            data: { serial_number: serialNumber },
            dataType: 'json',
            beforeSend: function() {
                $('#btn-validate-device').prop('disabled', true).text('验证中...');
                $('#device-status').html('<span class="text-info">正在验证设备...</span>');
            },
            success: function(ret) {
                if (ret.code === 1) {
                    deviceValidated = true;
                    $('#device-status').html('<span class="text-success">✓ 设备验证成功</span>');

                    // 填充设备信息到表单
                    var data = ret.data;
                    $('#c-g_device_id').val(data.g_id);

                    // 使用延迟设置selectpage值
                    setSelectpageValue('#c-g_game_id', data.g_game_id);
                    $('#c-g_total_plays').val(data.g_total_plays);
                    $('#c-g_total_wins').val(data.g_total_wins);

                    // 填充其他字段
                    if (data.g_mode !== undefined) $('#c-g_mode').val(data.g_mode);
                    if (data.g_wave !== undefined) $('#c-g_wave').val(data.g_wave);
                    if (data.g_rate !== undefined) $('#c-g_rate').val(data.g_rate);
                    if (data.g_back !== undefined) $('#c-g_back').val(data.g_back);
                    if (data.g_limit !== undefined) $('#c-g_limit').val(data.g_limit);
                    if (data.g_time !== undefined) $('#c-g_time').val(data.g_time);
                    if (data.g_enable !== undefined) $('#c-g_enable').val(data.g_enable);
                    if (data.g_enable_type !== undefined) $('#c-g_enable_type').val(data.g_enable_type);

                    // 设置复选框
                    $('#c-g_enable_modify_mode').prop('checked', data.g_enable_modify_mode == 1);
                    $('#c-g_enable_adjust_rate').prop('checked', data.g_enable_adjust_rate == 1);
                    $('#c-g_enable_clear_current_account').prop('checked', data.g_enable_clear_current_account == 1);
                    $('#c-g_enable_clear_total_account').prop('checked', data.g_enable_clear_total_account == 1);
                    $('#c-g_accept_modify_mode').prop('checked', data.g_accept_modify_mode == 1);
                    $('#c-g_accept_adjust_rate').prop('checked', data.g_accept_adjust_rate == 1);
                    $('#c-g_clear_current_account').prop('checked', data.g_clear_current_account == 1);
                    $('#c-g_clear_total_account').prop('checked', data.g_clear_total_account == 1);
                    $('#c-g_cannon_value').prop('checked', data.g_cannon_value == 1);
                    $('#c-g_fish_odds').prop('checked', data.g_fish_odds == 1);

                    // 执行回调
                    if (callback) callback(data);

                } else {
                    deviceValidated = false;
                    $('#device-status').html('<span class="text-danger">✗ ' + ret.msg + '</span>');
                }
            },
            error: function() {
                deviceValidated = false;
                $('#device-status').html('<span class="text-danger">✗ 验证失败，请重试</span>');
            },
            complete: function() {
                $('#btn-validate-device').prop('disabled', false).text('验证设备');
            }
        });
    }

    // 表单提交前验证
    $(document).on('submit', '#add-form', function(e) {
        if (!deviceValidated) {
            e.preventDefault();
            Toastr.error('请先验证设备后再提交');
            return false;
        }
    });

    // URL参数处理已移动到 device_record.js 中
</script>

<!-- 引入HTML5-QRCode库 -->
<script src="/assets/libs/html5-qrcode/html5-qrcode.min.js"></script>


    <div><input type="hidden" id="c-g_enable_type" name="row[g_enable_type]" value="{$params.g_enable_type|default=0}"></div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_device_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-g_device_id" data-rule="required" class="form-control" name="row[g_device_id]" type="text" value="{$params.g_device_id|htmlentities}" readonly>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_serial_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_serial_number" data-rule="required" class="form-control" name="row[g_serial_number]" type="text" value="{$params.g_serial_number|htmlentities}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_game_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input  id="c-g_game_id" data-rule="required" class="form-control" name="row[g_game_id]" type="text" value="{$params.g_game_id|htmlentities}" placeholder="请输入游戏ID">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_plays')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_plays" class="form-control" name="row[g_total_plays]" type="number" value="{$params.g_total_plays|default=0}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_wins')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_wins" class="form-control" name="row[g_total_wins]" type="number" value="{$params.g_total_wins|default=0}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_chk')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_chk" class="form-control" name="row[g_chk]" type="number" value="{$params.g_chk|default=''}">
        </div>
    </div>


    
    {if isset($params.g_enable_type) && $params.g_enable_type == 1}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_modify_mode" name="row[g_enable_modify_mode]" type="checkbox" value="1" {if isset($params.g_enable_modify_mode) && $params.g_enable_modify_mode == 1}checked{/if}> {:__('G_enable_modify_mode')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_adjust_rate" name="row[g_enable_adjust_rate]" type="checkbox" value="1" {if isset($params.g_enable_adjust_rate) && $params.g_enable_adjust_rate == 1}checked{/if}> {:__('G_enable_adjust_rate')}
                    </label>
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_clear_current_account" name="row[g_enable_clear_current_account]" type="checkbox" value="1" {if isset($params.g_enable_clear_current_account) && $params.g_enable_clear_current_account == 1}checked{/if}> {:__('G_enable_clear_current_account')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_clear_total_account" name="row[g_enable_clear_total_account]" type="checkbox" value="1" {if isset($params.g_enable_clear_total_account) && $params.g_enable_clear_total_account == 1}checked{/if}> {:__('G_enable_clear_total_account')}
                    </label>
                </div>
            </div>
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Accept Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_accept_modify_mode" name="row[g_accept_modify_mode]" type="checkbox" value="1" {if isset($params.g_accept_modify_mode) && $params.g_accept_modify_mode == 1}checked{/if}> {:__('G_accept_modify_mode')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_accept_adjust_rate" name="row[g_accept_adjust_rate]" type="checkbox" value="1" {if isset($params.g_accept_adjust_rate) && $params.g_accept_adjust_rate == 1}checked{/if}> {:__('G_accept_adjust_rate')}
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Clear Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_clear_current_account" name="row[g_clear_current_account]" type="checkbox" value="1" {if isset($params.g_clear_current_account) && $params.g_clear_current_account == 1}checked{/if}> {:__('G_clear_current_account')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_clear_total_account" name="row[g_clear_total_account]" type="checkbox" value="1" {if isset($params.g_clear_total_account) && $params.g_clear_total_account == 1}checked{/if}> {:__('G_clear_total_account')}
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.mode_config) && !isset($gameConfig.mode_config.range)}
                <select id="c-g_mode" class="form-control selectpicker" name="row[g_mode]">
                    {foreach name="gameConfig.mode_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if isset($params.g_mode) && (string)$params.g_mode === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_mode" class="form-control" name="row[g_mode]" type="number" value="{$params.g_mode|default=0}">
                {if isset($gameConfig.mode_config.range)}
                    <span class="help-block">{$gameConfig.mode_config.description|default=''} (范围: {$gameConfig.mode_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_wave')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.wave_config) && !isset($gameConfig.wave_config.range)}
                <select id="c-g_wave" class="form-control selectpicker" name="row[g_wave]">
                    {foreach name="gameConfig.wave_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if isset($params.g_wave) && (string)$params.g_wave === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_wave" class="form-control" name="row[g_wave]" type="number" value="{$params.g_wave|default=0}">
                {if isset($gameConfig.wave_config.range)}
                    <span class="help-block">{$gameConfig.wave_config.description|default=''} (范围: {$gameConfig.wave_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.rate_config) && !isset($gameConfig.rate_config.range)}
                <select id="c-g_rate" class="form-control selectpicker" name="row[g_rate]">
                    {foreach name="gameConfig.rate_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if isset($params.g_rate) && (string)$params.g_rate === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_rate" class="form-control" name="row[g_rate]" type="number" value="{$params.g_rate|default=0}">
                {if isset($gameConfig.rate_config.range)}
                    <span class="help-block">{$gameConfig.rate_config.description|default=''} (范围: {$gameConfig.rate_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_back')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.back_config) && !isset($gameConfig.back_config.range)}
                <select id="c-g_back" class="form-control selectpicker" name="row[g_back]">
                    {foreach name="gameConfig.back_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if isset($params.g_back) && (string)$params.g_back === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_back" class="form-control" name="row[g_back]" type="number" value="{$params.g_back|default=0}">
                {if isset($gameConfig.back_config.range)}
                    <span class="help-block">{$gameConfig.back_config.description|default=''} (范围: {$gameConfig.back_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>


    {if isset($params.g_enable_type) && $params.g_enable_type == 2}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_enable')}:</label>
        <div class="col-xs-12 col-sm-8">
                {if isset($gameConfig.enable_config) && !isset($gameConfig.enable_config.range)}
                    <select id="c-g_enable" class="form-control selectpicker" name="row[g_enable]">
                        {foreach name="gameConfig.enable_config" item="item" key="key"}
                            {if is_array($item) && isset($item.key) && isset($item.value)}
                                <option value="{$item.key}" {if isset($params.g_enable) && (string)$params.g_enable === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                            {/if}
                        {/foreach}
                    </select>
                {else}
                    <input id="c-g_enable" class="form-control" name="row[g_enable]" type="number" value="{$params.g_enable|default=0}">
                    {if isset($gameConfig.enable_config.range)}
                        <span class="help-block">{$gameConfig.enable_config.description|default=''} (范围: {$gameConfig.enable_config.range})</span>
                    {/if}
                {/if}
        </div>
        
    </div>
    {/if}


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_limit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_limit" class="form-control" name="row[g_limit]" type="number" value="{$params.g_limit|default=0}">
            {if isset($gameConfig.limit_config.range)}
                <span class="help-block">{$gameConfig.limit_config.description|default=''} (范围: {$gameConfig.limit_config.range})</span>
            {/if}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('g_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_time" class="form-control" name="row[g_time]" type="number" value="{$params.g_time|default=0}">
            {if isset($gameConfig.time_config.range)}
                <span class="help-block">{$gameConfig.time_config.description|default=''} (范围: {$gameConfig.time_config.range})</span>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <div class="col-xs-6">
            <label class="checkbox-inline">
                <input id="c-g_cannon_value" name="row[g_cannon_value]" type="checkbox" value="1" {if isset($params.g_cannon_value) && $params.g_cannon_value == 1}checked{/if}> {:__('G_cannon_value')}
            </label>
        </div>
        <div class="col-xs-6">
            <label class="checkbox-inline">
                <input id="c-g_fish_odds" name="row[g_fish_odds]" type="checkbox" value="1" {if isset($params.g_fish_odds) && $params.g_fish_odds == 1}checked{/if}> {:__('G_fish_odds')}
            </label>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
