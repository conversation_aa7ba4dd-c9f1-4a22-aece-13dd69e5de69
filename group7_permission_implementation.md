# Group ID=7 用户权限控制实现

## 概述

已成功实现了 group_id=7 用户的权限控制，确保这些用户只能查看和操作有权限的设备和打码记录。权限基于 `g_merchant_id` 字段进行控制。

## 实现的功能

### 1. 设备控制器权限控制 (Device.php)

**修改内容：**
- 启用数据限制：`$dataLimit = 'merchant'`
- 设置限制字段：`$dataLimitField = 'g_merchant_id'`
- 重写 `getDataLimitAdminIds()` 方法
- 添加必要的异常类导入

**权限逻辑：**
- group_id=7 用户只能查看 `g_merchant_id = 当前用户ID` 的设备
- 如果用户在 gc_device 和 gc_device_record 表中都没有记录，则无权限
- 自动在添加设备时设置 `g_merchant_id = 当前用户ID`

### 2. 设备记录控制器权限控制 (DeviceRecord.php)

**修改内容：**
- 启用数据限制：`$dataLimit = 'merchant'`
- 设置限制字段：`$dataLimitField = 'g_merchant_id'`
- 重写 `getDataLimitAdminIds()` 方法
- 修正字段名称：`g_merchant_id` 而不是 `merchant_id`

**权限逻辑：**
- group_id=7 用户只能查看和操作 `g_merchant_id = 当前用户ID` 的记录
- 创建记录时自动设置 `g_merchant_id = 当前用户ID`
- 创建的记录状态自动设为 'pending'（审核中）

## 权限验证流程

### getDataLimitAdminIds() 方法逻辑

```php
// 对于 group_id=7 的商户用户，直接限制只能查看自己的数据
if ($this->auth->group_id == 7) {
    // 返回当前用户ID，用于 g_merchant_id 字段过滤
    return [$this->auth->id];
}
```

**简化说明：**
- group_id=7 的商户用户直接返回自己的用户ID
- 不需要检查表中是否存在数据
- 系统自动在查询时添加 `WHERE g_merchant_id = 用户ID` 条件

## 应用场景

权限控制在以下场景中生效：

1. **列表查询 (index 方法)**
   - 通过 `buildparams()` 方法自动添加 WHERE 条件
   - 只显示有权限的记录

2. **编辑权限检查 (edit 方法)**
   - 检查记录的 `g_merchant_id` 是否在允许的ID列表中
   - 无权限时显示 "You have no permission" 错误

3. **删除权限检查 (del 方法)**
   - 通过 Backend trait 自动应用权限过滤

4. **Selectpage 下拉选择**
   - 在 `selectpage()` 方法中自动过滤数据

## 测试场景

### 商户用户权限控制
- 用户ID=100，group_id=7（商户用户）
- 结果：只能查看和操作 `g_merchant_id=100` 的设备和记录
- 说明：不管表中是否有数据，商户用户都只能看到属于自己的数据

## 验证步骤

1. 创建 group_id=7 的商户测试用户
2. 在数据库中添加测试数据（一些记录的 g_merchant_id 等于测试用户ID，一些不等于）
3. 登录测试用户验证以下功能：
   - 设备列表只显示 g_merchant_id=用户ID 的设备
   - 打码记录列表只显示 g_merchant_id=用户ID 的记录
   - 无法编辑其他商户的记录
   - 创建记录时自动设置 g_merchant_id=当前用户ID
   - 创建的记录状态为 'pending'

## 注意事项

1. **简化逻辑**：group_id=7 用户直接返回自己的ID，无需检查表中是否存在数据
2. **字段名称**：确保使用正确的字段名 `g_merchant_id`
3. **权限继承**：其他用户组仍使用原有的权限控制逻辑
4. **数据一致性**：确保创建记录时正确设置所有必要字段

## 文件修改清单

- `application/admin/controller/device/Device.php` - 添加权限控制
- `application/admin/controller/device/DeviceRecord.php` - 添加权限控制
- `test_group7_permissions.php` - 测试脚本
- `group7_permission_implementation.md` - 实现文档
