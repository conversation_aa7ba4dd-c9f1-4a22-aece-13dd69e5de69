<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
<script>
    // 游戏配置数据
    var gameList = {:json_encode($gameList)};
    var currentGameId = "{$currentGameId|default=''}";

    // 监听游戏选择变化
    $(document).on('change', '#c-g_game_id', function() {
        var gameId = $(this).val();
        if (!gameId || !gameList[gameId]) {
            return;
        }

        // 重新加载页面，带上游戏ID参数
        var url = location.href.split('?')[0] + '?g_game_id=' + gameId;
        location.href = url;
    });
</script>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('打码结果')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-g_api_msg" class="form-control" name="row[g_api_msg]" rows="3">{$row.g_api_msg|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_device_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_device_id" data-rule="required" data-source="g/device/index" class="form-control selectpage" name="row[g_device_id]" type="text" value="{$row.g_device_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_serial_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_serial_number" data-rule="required" class="form-control" name="row[g_serial_number]" type="text" value="{$row.g_serial_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_game_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_game_id" data-rule="required" class="form-control" name="row[g_game_id]" type="text" value="{$row.g_game_id|htmlentities}" placeholder="请输入游戏ID">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_plays')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_plays" class="form-control" name="row[g_total_plays]" type="number" value="{$row.g_total_plays|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_wins')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_wins" class="form-control" name="row[g_total_wins]" type="number" value="{$row.g_total_wins|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enable Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_modify_mode" name="row[g_enable_modify_mode]" type="checkbox" value="1" {$row.g_enable_modify_mode?'checked':''}> {:__('G_enable_modify_mode')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_adjust_rate" name="row[g_enable_adjust_rate]" type="checkbox" value="1" {$row.g_enable_adjust_rate?'checked':''}> {:__('G_enable_adjust_rate')}
                    </label>
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_clear_current_account" name="row[g_enable_clear_current_account]" type="checkbox" value="1" {$row.g_enable_clear_current_account?'checked':''}> {:__('G_enable_clear_current_account')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_enable_clear_total_account" name="row[g_enable_clear_total_account]" type="checkbox" value="1" {$row.g_enable_clear_total_account?'checked':''}> {:__('G_enable_clear_total_account')}
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Accept Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_accept_modify_mode" name="row[g_accept_modify_mode]" type="checkbox" value="1" {$row.g_accept_modify_mode?'checked':''}> {:__('G_accept_modify_mode')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_accept_adjust_rate" name="row[g_accept_adjust_rate]" type="checkbox" value="1" {$row.g_accept_adjust_rate?'checked':''}> {:__('G_accept_adjust_rate')}
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Clear Options')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_clear_current_account" name="row[g_clear_current_account]" type="checkbox" value="1" {$row.g_clear_current_account?'checked':''}> {:__('G_clear_current_account')}
                    </label>
                </div>
                <div class="col-xs-6">
                    <label class="checkbox-inline">
                        <input id="c-g_clear_total_account" name="row[g_clear_total_account]" type="checkbox" value="1" {$row.g_clear_total_account?'checked':''}> {:__('G_clear_total_account')}
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.mode_config) && !isset($gameConfig.mode_config.range)}
                <select id="c-g_mode" class="form-control selectpicker" name="row[g_mode]">
                    {foreach name="gameConfig.mode_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if (string)$row.g_mode === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_mode" class="form-control" name="row[g_mode]" type="number" value="{$row.g_mode|htmlentities}">
                {if isset($gameConfig.mode_config.range)}
                    <span class="help-block">{$gameConfig.mode_config.description|default=''} (范围: {$gameConfig.mode_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_wave')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.wave_config) && !isset($gameConfig.wave_config.range)}
                <select id="c-g_wave" class="form-control selectpicker" name="row[g_wave]">
                    {foreach name="gameConfig.wave_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if (string)$row.g_wave === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_wave" class="form-control" name="row[g_wave]" type="number" value="{$row.g_wave|htmlentities}">
                {if isset($gameConfig.wave_config.range)}
                    <span class="help-block">{$gameConfig.wave_config.description|default=''} (范围: {$gameConfig.wave_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.rate_config) && !isset($gameConfig.rate_config.range)}
                <select id="c-g_rate" class="form-control selectpicker" name="row[g_rate]">
                    {foreach name="gameConfig.rate_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if (string)$row.g_rate === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_rate" class="form-control" name="row[g_rate]" type="number" value="{$row.g_rate|htmlentities}">
                {if isset($gameConfig.rate_config.range)}
                    <span class="help-block">{$gameConfig.rate_config.description|default=''} (范围: {$gameConfig.rate_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_back')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if isset($gameConfig.back_config) && !isset($gameConfig.back_config.range)}
                <select id="c-g_back" class="form-control selectpicker" name="row[g_back]">
                    {foreach name="gameConfig.back_config" item="item" key="key"}
                        {if is_array($item) && isset($item.key) && isset($item.value)}
                            <option value="{$item.key}" {if (string)$row.g_back === (string)$item.key}selected{/if}>{$item.key} = {$item.value}</option>
                        {/if}
                    {/foreach}
                </select>
            {else}
                <input id="c-g_back" class="form-control" name="row[g_back]" type="number" value="{$row.g_back|htmlentities}">
                {if isset($gameConfig.back_config.range)}
                    <span class="help-block">{$gameConfig.back_config.description|default=''} (范围: {$gameConfig.back_config.range})</span>
                {/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_limit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_limit" class="form-control" name="row[g_limit]" type="number" value="{$row.g_limit|htmlentities}">
            {if isset($gameConfig.limit_config.range)}
                <span class="help-block">{$gameConfig.limit_config.description|default=''} (范围: {$gameConfig.limit_config.range})</span>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_time" class="form-control" name="row[g_time]" type="text" value="{:$row.g_time?$row.g_time:''}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('g_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_time" class="form-control" name="row[g_time]" type="number" value="{$row.g_time|htmlentities|default=0}">
            {if isset($gameConfig.time_config.range)}
                <span class="help-block">{$gameConfig.time_config.description|default=''} (范围: {$gameConfig.time_config.range})</span>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_coin_score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_coin_score" class="form-control" name="row[g_coin_score]" type="number" value="{$row.g_coin_score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_cannon_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_cannon_value" class="form-control" name="row[g_cannon_value]" type="number" value="{$row.g_cannon_value|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_fish_odds')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_fish_odds" class="form-control" name="row[g_fish_odds]" type="number" value="{$row.g_fish_odds|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_createtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_createtime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_createtime]" type="text" value="{:$row.g_createtime?datetime($row.g_createtime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_updatetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_updatetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_updatetime]" type="text" value="{:$row.g_updatetime?datetime($row.g_updatetime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_deletetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_deletetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_deletetime]" type="text" value="{:$row.g_deletetime?datetime($row.g_deletetime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_admin_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_admin_id" data-rule="required" data-source="auth/admin/index" class="form-control selectpage" name="row[g_admin_id]" type="text" value="{$row.g_admin_id|htmlentities}" data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"6"}'>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_merchant_id" data-rule="required" data-source="auth/admin/index" class="form-control selectpage" name="row[g_merchant_id]" type="text" value="{$row.g_merchant_id|htmlentities}" data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"7"}'>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_operator_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_operator_id" data-rule="required" data-source="auth/admin/index" class="form-control selectpage" name="row[g_operator_id]" type="text" value="{$row.g_operator_id|htmlentities}" data-primary-key="id" data-field="nickname">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="gStatusList" item="vo"}
            <label for="row[g_status]-{$key}"><input id="row[g_status]-{$key}" name="row[g_status]" type="radio" value="{$key}" {in name="key" value="$row.g_status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
