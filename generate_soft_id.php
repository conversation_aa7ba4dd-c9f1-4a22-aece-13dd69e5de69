<?php
// 引入ThinkPHP
require __DIR__ . '/thinkphp/start.php';

use think\Db;

// 生成32位唯一ID
function generateSoftId() {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $softId = '';
    for ($i = 0; $i < 32; $i++) {
        $softId .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $softId;
}

// 查询所有设备
$devices = Db::table('gc_device')->select();

if ($devices) {
    // 输出数据
    echo "开始为设备生成唯一ID...\n";
    foreach ($devices as $device) {
        $g_id = $device["g_id"];
        $g_soft_id = $device["g_soft_id"];
        
        // 如果g_soft_id为空，则生成一个新的
        if (empty($g_soft_id)) {
            $new_soft_id = generateSoftId();
            $result = Db::table('gc_device')->where('g_id', $g_id)->update(['g_soft_id' => $new_soft_id]);
            if ($result) {
                echo "设备ID: $g_id 的唯一ID已更新为: $new_soft_id\n";
            } else {
                echo "更新设备ID: $g_id 失败\n";
            }
        } else {
            echo "设备ID: $g_id 已有唯一ID: $g_soft_id\n";
        }
    }
    echo "完成！\n";
} else {
    echo "没有找到设备记录\n";
}
?>
