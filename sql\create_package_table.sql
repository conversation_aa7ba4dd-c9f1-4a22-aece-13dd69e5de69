-- 创建打包表
CREATE TABLE `gc_package` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `board_id` varchar(50) NOT NULL COMMENT '主板ID',
  `encrypt_id` varchar(50) NOT NULL COMMENT '加密ID',
  `admin_id` int(11) NOT NULL COMMENT '关联管理员ID',
  `createtime` int(11) DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `board_id` (`board_id`),
  UNIQUE KEY `encrypt_id` (`encrypt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打包表';
