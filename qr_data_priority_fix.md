# 二维码数据优先级修复

## 问题描述

在后端处理二维码数据时，数据库中的设备信息覆盖了二维码中的 `playin`（总玩次数）和 `playout`（总赢次数）值，导致这些字段显示为 0 而不是二维码中的实际值。

## 问题原因

原来的代码逻辑：
```php
// 先映射二维码数据到参数
foreach ($qrData as $key => $value) {
    if (isset($fieldMapping[$key])) {
        $params[$fieldMapping[$key]] = $value;  // 设置二维码值
    }
}

// 然后无条件覆盖数据库值
$params['g_total_plays'] = $device['g_total_plays'];   // 覆盖了二维码值！
$params['g_total_wins'] = $device['g_total_wins'];     // 覆盖了二维码值！
```

## 修复方案

修改后的代码逻辑：
```php
// 补充设备ID和其他设备信息
$params['g_device_id'] = $device['g_id'];

// 只有当二维码中没有这些字段时才使用数据库中的值
if (!isset($params['g_game_id']) || empty($params['g_game_id'])) {
    $params['g_game_id'] = $device['g_game_id'];
}
if (!isset($params['g_total_plays']) || $params['g_total_plays'] === '') {
    $params['g_total_plays'] = $device['g_total_plays'];
}
if (!isset($params['g_total_wins']) || $params['g_total_wins'] === '') {
    $params['g_total_wins'] = $device['g_total_wins'];
}
```

## 数据优先级

### 新的优先级规则

1. **二维码数据优先**：如果二维码中包含某个字段的值，优先使用二维码中的值
2. **数据库补充**：只有当二维码中没有该字段或值为空时，才使用数据库中的值
3. **必需字段**：`g_device_id` 始终从数据库获取（因为二维码中通常没有）

### 字段处理策略

| 字段 | 处理策略 | 说明 |
|------|---------|------|
| g_device_id | 始终使用数据库值 | 二维码中通常没有此字段 |
| g_game_id | 二维码优先 | 如果二维码中没有则使用数据库值 |
| g_total_plays | 二维码优先 | 二维码中的实时数据更准确 |
| g_total_wins | 二维码优先 | 二维码中的实时数据更准确 |
| 其他配置字段 | 使用数据库值 | 设备配置信息以数据库为准 |

## 测试场景

### 场景1：二维码包含完整数据
```json
{
    "dev": "00900356",
    "game": "1005",
    "playin": "528930",
    "playout": "531090"
}
```

**预期结果：**
- g_serial_number: "00900356"
- g_game_id: "1005" (来自二维码)
- g_total_plays: "528930" (来自二维码)
- g_total_wins: "531090" (来自二维码)
- g_device_id: 从数据库获取

### 场景2：二维码缺少部分数据
```json
{
    "dev": "00900356",
    "playin": "528930"
}
```

**预期结果：**
- g_serial_number: "00900356"
- g_game_id: 从数据库获取
- g_total_plays: "528930" (来自二维码)
- g_total_wins: 从数据库获取
- g_device_id: 从数据库获取

### 场景3：二维码数据为空值
```json
{
    "dev": "00900356",
    "game": "",
    "playin": "0",
    "playout": ""
}
```

**预期结果：**
- g_serial_number: "00900356"
- g_game_id: 从数据库获取 (二维码值为空)
- g_total_plays: "0" (来自二维码，即使是0也是有效值)
- g_total_wins: 从数据库获取 (二维码值为空字符串)
- g_device_id: 从数据库获取

## 判断逻辑

### 空值判断
```php
// 检查字段是否存在且不为空
if (!isset($params['g_total_plays']) || $params['g_total_plays'] === '') {
    $params['g_total_plays'] = $device['g_total_plays'];
}
```

**判断条件说明：**
- `!isset($params['g_total_plays'])`: 字段不存在
- `$params['g_total_plays'] === ''`: 字段存在但值为空字符串
- **注意**：数字 0 被认为是有效值，不会被数据库值覆盖

### 特殊情况处理

1. **数字 0**：被认为是有效的二维码数据，不会被覆盖
2. **空字符串**：被认为是无效数据，会使用数据库值
3. **null 值**：被认为是无效数据，会使用数据库值
4. **未定义**：会使用数据库值

## 验证方法

### 1. 检查表单显示
扫码后检查添加页面中的字段值：
```javascript
console.log('总玩次数：', $('#c-g_total_plays').val());
console.log('总赢次数：', $('#c-g_total_wins').val());
```

### 2. 检查后端日志
在控制器中添加调试信息：
```php
error_log('二维码数据：' . json_encode($qrData));
error_log('最终参数：' . json_encode($params));
```

### 3. 数据库对比
对比二维码中的值和数据库中的值，确认优先级正确。

## 相关文件

- `application/admin/controller/device/DeviceRecord.php` - 主要修改文件
- 修改位置：`add()` 方法中的二维码数据处理部分

## 注意事项

1. **数据类型**：确保二维码中的数字字符串能正确处理
2. **空值定义**：明确什么情况下认为是"空值"
3. **向后兼容**：确保修改不影响非二维码的正常添加流程
4. **性能影响**：增加的判断逻辑对性能影响很小

## 测试建议

1. **完整数据测试**：使用包含所有字段的二维码测试
2. **部分数据测试**：使用缺少部分字段的二维码测试
3. **空值测试**：使用包含空值的二维码测试
4. **边界测试**：测试数字0、空字符串等边界情况
5. **权限测试**：确保权限检查仍然正常工作
