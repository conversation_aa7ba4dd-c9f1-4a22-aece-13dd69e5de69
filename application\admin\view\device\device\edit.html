<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_soft_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_soft_id" class="form-control" name="row[g_soft_id]" type="text" value="{$row.g_soft_id|htmlentities}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_serial_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_serial_number" data-rule="required" class="form-control" name="row[g_serial_number]" type="text" value="{$row.g_serial_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_rid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_rid" class="form-control" name="row[g_rid]" type="text" value="{$row.g_rid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_batch_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_batch_number" class="form-control" name="row[g_batch_number]" type="text" value="{$row.g_batch_number|htmlentities}">
        </div>
    </div> 
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_game_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input  id="c-g_game_id" data-rule="required" class="form-control selectpage" name="row[g_game_id]" data-source="game/index" data-field="game_name" data-primary-key="game_code" value="{$row.g_game_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_plays')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_plays" class="form-control" name="row[g_total_plays]" type="number" value="{$row.g_total_plays|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_total_wins')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_total_wins" class="form-control" name="row[g_total_wins]" type="number" value="{$row.g_total_wins|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_enable_modify_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_enable_modify_mode" class="form-control" name="row[g_enable_modify_mode]" type="number" value="{$row.g_enable_modify_mode|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_enable_adjust_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_enable_adjust_rate" class="form-control" name="row[g_enable_adjust_rate]" type="number" value="{$row.g_enable_adjust_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_enable_clear_current_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_enable_clear_current_account" class="form-control" name="row[g_enable_clear_current_account]" type="number" value="{$row.g_enable_clear_current_account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_enable_clear_total_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_enable_clear_total_account" class="form-control" name="row[g_enable_clear_total_account]" type="number" value="{$row.g_enable_clear_total_account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_accept_modify_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_accept_modify_mode" class="form-control" name="row[g_accept_modify_mode]" type="number" value="{$row.g_accept_modify_mode|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_accept_adjust_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_accept_adjust_rate" class="form-control" name="row[g_accept_adjust_rate]" type="number" value="{$row.g_accept_adjust_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_clear_current_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_clear_current_account" class="form-control" name="row[g_clear_current_account]" type="number" value="{$row.g_clear_current_account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_clear_total_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_clear_total_account" class="form-control" name="row[g_clear_total_account]" type="number" value="{$row.g_clear_total_account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_mode" class="form-control" name="row[g_mode]" type="number" value="{$row.g_mode|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_mode_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_mode_config" class="form-control" name="row[g_mode_config]" type="text" value="{$row.g_mode_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_wave')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_wave" class="form-control" name="row[g_wave]" type="number" value="{$row.g_wave|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_wave_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_wave_config" class="form-control" name="row[g_wave_config]" type="text" value="{$row.g_wave_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_rate" class="form-control" name="row[g_rate]" type="number" value="{$row.g_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_rate_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_rate_config" class="form-control" name="row[g_rate_config]" type="text" value="{$row.g_rate_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_back')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_back" class="form-control" name="row[g_back]" type="number" value="{$row.g_back|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_back_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_back_config" class="form-control" name="row[g_back_config]" type="text" value="{$row.g_back_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_limit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_limit" class="form-control" name="row[g_limit]" type="number" value="{$row.g_limit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_limit_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_limit_config" class="form-control" name="row[g_limit_config]" type="text" value="{$row.g_limit_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_time" class="form-control" name="row[g_time]" type="text" value="{:$row.g_time?$row.g_time:'0'}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_time_config')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_time_config" class="form-control" name="row[g_time_config]" type="text" value="{$row.g_time_config|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_coin_score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_coin_score" class="form-control" name="row[g_coin_score]" type="number" value="{$row.g_coin_score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_cannon_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_cannon_value" class="form-control" name="row[g_cannon_value]" type="number" value="{$row.g_cannon_value|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_fish_odds')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_fish_odds" class="form-control" name="row[g_fish_odds]" type="number" value="{$row.g_fish_odds|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="gStatusList" item="vo"}
            <label for="row[g_status]-{$key}"><input id="row[g_status]-{$key}" name="row[g_status]" type="radio" value="{$key}" {in name="key" value="$row.g_status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_createtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_createtime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_createtime]" type="text" value="{:$row.g_createtime?datetime($row.g_createtime):''}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_updatetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_updatetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_updatetime]" type="text" value="{:$row.g_updatetime?datetime($row.g_updatetime):''}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_deletetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_deletetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[g_deletetime]" type="text" value="{:$row.g_deletetime?datetime($row.g_deletetime):''}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_admin_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_admin_id" data-rule="" data-source="auth/admin/index" class="form-control selectpage" name="row[g_admin_id]" type="text" value="{$row.g_admin_id|htmlentities}" data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"6"}'>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('G_merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-g_merchant_id" data-rule="" data-source="auth/admin/index" class="form-control selectpage" name="row[g_merchant_id]" type="text" value="{$row.g_merchant_id|htmlentities}" data-primary-key="id" data-field="nickname" data-params='{"custom[group_id]":"7"}'>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
